package net.pandaboots.pandabootsstatus.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.texture.NativeImageBackedTexture;
import net.minecraft.resource.Resource;
import net.minecraft.util.Identifier;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.status.MoodStatus;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Improved external PNG emoji renderer that manually loads PNG files from resources
 */
public class ImprovedExternalPngRenderer {
    private static final Map<String, Identifier> loadedTextures = new HashMap<>();
    private static boolean initialized = false;
    private static final int EMOJI_SIZE = 8;   // Half size from 16 to 8
    
    /**
     * Initializes the improved external PNG emoji system
     */
    public static void initialize() {
        if (initialized) return;
        
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null) {
                PandaBootsStatus.LOGGER.warn("Client not available for emoji initialization");
                return;
            }
            
            // Try to load each emoji PNG file using the correct names
            loadEmojiTexture("panda");
            loadEmojiTexture("aggressive");  // Maps to aggressive.png
            loadEmojiTexture("playful");     // Maps to playful.png
            loadEmojiTexture("lazy");        // Maps to lazy.png
            
            initialized = true;
            PandaBootsStatus.LOGGER.info("Improved external PNG emoji renderer initialized with {} textures", loadedTextures.size());
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to initialize improved external PNG emoji renderer", e);
            initialized = true;
        }
    }
    
    /**
     * Attempts to load a PNG file and register it as a texture
     */
    private static void loadEmojiTexture(String emojiName) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null || client.getResourceManager() == null) {
                PandaBootsStatus.LOGGER.debug("Client or resource manager not available for {}", emojiName);
                return;
            }
            
            // Try to find the PNG file in resources
            Identifier resourceId = Identifier.of("panda-boots-status", "textures/emoji/" + emojiName + ".png");
            
            Optional<Resource> resourceOpt = client.getResourceManager().getResource(resourceId);
            if (resourceOpt.isEmpty()) {
                PandaBootsStatus.LOGGER.debug("PNG file not found: {}", resourceId);
                return;
            }
            
            Resource resource = resourceOpt.get();
            
            // Load the PNG file
            try (InputStream inputStream = resource.getInputStream()) {
                NativeImage image = NativeImage.read(inputStream);
                
                // Create a texture from the image
                NativeImageBackedTexture texture = new NativeImageBackedTexture(image);
                
                // Register the texture with a unique identifier
                Identifier textureId = Identifier.of("panda-boots-status", "emoji_loaded_" + emojiName);
                client.getTextureManager().registerTexture(textureId, texture);
                
                loadedTextures.put(emojiName, textureId);
                PandaBootsStatus.LOGGER.info("Successfully loaded external PNG emoji: {} -> {}", emojiName, textureId);
                
            } catch (Exception imageError) {
                PandaBootsStatus.LOGGER.warn("Failed to load image for {}: {}", emojiName, imageError.getMessage());
            }
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.warn("Failed to load emoji texture {}: {}", emojiName, e.getMessage());
        }
    }
    
    /**
     * Checks if an emoji texture was successfully loaded
     */
    public static boolean hasEmojiTexture(String emojiName) {
        if (!initialized) {
            initialize();
        }
        
        boolean hasTexture = loadedTextures.containsKey(emojiName);
        PandaBootsStatus.LOGGER.debug("Emoji {} texture check: {}", emojiName, hasTexture ? "Available" : "Missing");
        return hasTexture;
    }
    
    /**
     * Renders an emoji from loaded PNG texture
     */
    public static boolean renderEmoji(DrawContext context, String emojiName, int x, int y, float scale) {
        if (!initialized) {
            initialize();
        }
        
        Identifier textureId = loadedTextures.get(emojiName);
        if (textureId == null) {
            PandaBootsStatus.LOGGER.debug("No loaded texture for emoji: {}", emojiName);
            return false;
        }
        
        try {
            int size = (int) (EMOJI_SIZE * scale);
            
            // Render the loaded texture
            context.drawTexture(textureId, x, y, 0, 0, size, size, size, size);
            
            PandaBootsStatus.LOGGER.debug("Successfully rendered loaded PNG emoji: {} at ({}, {}) size {}", emojiName, x, y, size);
            return true;
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.warn("Failed to render loaded emoji {}: {}", emojiName, e.getMessage());
            return false;
        }
    }
    
    /**
     * Gets the emoji name for a mood status
     * Maps mood status to PNG filename (without .png extension)
     */
    public static String getEmojiName(MoodStatus status) {
        String emojiName;
        switch (status) {
            case AGGRESSIVE:
                emojiName = "aggressive";  // Maps to aggressive.png
                break;
            case LAZY:
                emojiName = "lazy";        // Maps to lazy.png
                break;
            case PLAYFUL:
                emojiName = "playful";     // Maps to playful.png
                break;
            default:
                emojiName = null;
                break;
        }

        PandaBootsStatus.LOGGER.debug("Mood {} maps to emoji name: {}", status, emojiName);
        return emojiName;
    }
    
    /**
     * Renders a mood emoji from loaded PNG
     */
    public static boolean renderMoodEmoji(DrawContext context, MoodStatus status, int x, int y, float scale) {
        String emojiName = getEmojiName(status);
        PandaBootsStatus.LOGGER.debug("Rendering mood emoji for status {} using emoji name: {}", status, emojiName);

        if (emojiName != null) {
            boolean success = renderEmoji(context, emojiName, x, y, scale);
            PandaBootsStatus.LOGGER.debug("Mood emoji render result for {}: {}", status, success ? "SUCCESS" : "FAILED");
            return success;
        }

        PandaBootsStatus.LOGGER.debug("No emoji name found for mood status: {}", status);
        return false;
    }
    
    /**
     * Renders the panda emoji from loaded PNG
     */
    public static boolean renderPandaEmoji(DrawContext context, int x, int y, float scale) {
        return renderEmoji(context, "panda", x, y, scale);
    }
    
    /**
     * Gets the width of an emoji when rendered
     */
    public static int getEmojiWidth(float scale) {
        return (int) (EMOJI_SIZE * scale);
    }
    
    /**
     * Gets the height of an emoji when rendered
     */
    public static int getEmojiHeight(float scale) {
        return (int) (EMOJI_SIZE * scale);
    }
    
    /**
     * Gets debug information about loaded textures
     */
    public static String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Improved External PNG Emoji Renderer Status:\n");
        info.append("Initialized: ").append(initialized).append("\n");
        info.append("Loaded textures: ").append(loadedTextures.size()).append("\n");
        
        for (Map.Entry<String, Identifier> entry : loadedTextures.entrySet()) {
            info.append("  ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }
        
        return info.toString();
    }
    
    /**
     * Gets the number of successfully loaded emoji textures
     */
    public static int getLoadedTextureCount() {
        return loadedTextures.size();
    }
}
