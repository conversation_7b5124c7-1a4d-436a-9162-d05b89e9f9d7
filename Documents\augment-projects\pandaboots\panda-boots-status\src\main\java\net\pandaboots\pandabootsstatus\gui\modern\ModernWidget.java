package net.pandaboots.pandabootsstatus.gui.modern;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.widget.ClickableWidget;
import net.minecraft.client.gui.screen.narration.NarrationMessageBuilder;
import net.minecraft.text.Text;
import java.util.ArrayList;
import java.util.List;

/**
 * Base class for modern UI widgets with consistent styling
 * Adapted from readwork mod's ModernWidget
 */
public abstract class ModernWidget extends ClickableWidget {
    
    // Modern color scheme - Contemporary dark theme with high contrast
    public static final int PRIMARY_COLOR = 0xFF1A1A1A;      // Deep dark background
    public static final int SECONDARY_COLOR = 0xFF2A2A2A;    // Secondary background for cards
    public static final int BACKGROUND_COLOR = 0xF0121212;   // Main background with opacity
    public static final int BACKGROUND_SECONDARY = 0xF01E1E1E; // Card background

    public static final int BORDER_COLOR = 0xFF2D2D2D;       // Subtle border color
    public static final int BORDER_ACCENT = 0xFF404040;      // Accent border for focus states
    public static final int BORDER_FOCUS = 0xFF3B82F6;       // Focus border (blue)

    public static final int TEXT_COLOR = 0xFFE8E8E8;         // High contrast white text
    public static final int TEXT_SECONDARY = 0xFFB0B0B0;     // Secondary text with good contrast
    public static final int TEXT_MUTED = 0xFF808080;         // Muted text for less important info

    public static final int ACCENT_COLOR = 0xFF3B82F6;       // Modern blue accent (Tailwind blue-500)
    public static final int ACCENT_HOVER = 0xFF2563EB;       // Darker blue for hover (Tailwind blue-600)
    public static final int SUCCESS_COLOR = 0xFF10B981;      // Modern green (Tailwind emerald-500)
    public static final int WARNING_COLOR = 0xFFF59E0B;      // Modern amber (Tailwind amber-500)
    public static final int ERROR_COLOR = 0xFFEF4444;        // Modern red (Tailwind red-500)

    public static final int HOVER_COLOR = 0xFF2A2A2A;        // Subtle hover background
    public static final int ACTIVE_COLOR = 0xFF363636;       // Active/pressed state
    public static final int FOCUS_COLOR = 0xFF4A90E2;        // Focus ring color

    // Shadow and depth colors
    public static final int SHADOW_COLOR = 0x40000000;       // Subtle shadow
    public static final int SHADOW_STRONG = 0x60000000;      // Stronger shadow for elevation

    // Spacing and layout constants
    public static final int BORDER_RADIUS = 6;               // Modern rounded corners
    public static final int PADDING_SMALL = 8;               // Small padding
    public static final int PADDING_MEDIUM = 12;             // Medium padding
    public static final int PADDING_LARGE = 16;              // Large padding
    public static final int SPACING_SMALL = 4;               // Small spacing between elements
    public static final int SPACING_MEDIUM = 8;              // Medium spacing
    public static final int SPACING_LARGE = 12;              // Large spacing
    
    protected boolean isHovered = false;
    protected boolean isPressed = false;
    
    public ModernWidget(int x, int y, int width, int height, Text message) {
        super(x, y, width, height, message);
    }
    
    @Override
    public void renderWidget(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update hover state
        isHovered = mouseX >= getX() && mouseY >= getY() && 
                   mouseX < getX() + getWidth() && mouseY < getY() + getHeight();
        
        renderBackground(context, mouseX, mouseY, delta);
        renderContent(context, mouseX, mouseY, delta);
        renderBorder(context, mouseX, mouseY, delta);
    }
    
    /**
     * Renders the widget background with modern styling
     */
    protected void renderBackground(DrawContext context, int mouseX, int mouseY, float delta) {
        int backgroundColor = getBackgroundColor();

        // Draw subtle shadow for depth
        if (isHovered || isPressed) {
            context.fill(getX() + 1, getY() + 1, getX() + getWidth() + 1, getY() + getHeight() + 1, SHADOW_COLOR);
        }

        // Draw main background with rounded corners effect (simulated with border)
        context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), backgroundColor);

        // Add subtle inner highlight for modern look
        if (isHovered) {
            context.fill(getX() + 1, getY() + 1, getX() + getWidth() - 1, getY() + 2, 0x20FFFFFF);
        }
    }
    
    /**
     * Renders the widget content (text, icons, etc.)
     */
    protected abstract void renderContent(DrawContext context, int mouseX, int mouseY, float delta);
    
    /**
     * Renders the widget border with modern styling
     */
    protected void renderBorder(DrawContext context, int mouseX, int mouseY, float delta) {
        int borderColor = getBorderColor();

        // Draw main border
        context.drawBorder(getX(), getY(), getWidth(), getHeight(), borderColor);

        // Add focus ring for accessibility
        if (isFocused()) {
            context.drawBorder(getX() - 1, getY() - 1, getWidth() + 2, getHeight() + 2, BORDER_FOCUS);
        }
    }

    /**
     * Gets the background color based on widget state
     */
    protected int getBackgroundColor() {
        if (isPressed) return ACTIVE_COLOR;
        if (isHovered) return HOVER_COLOR;
        return PRIMARY_COLOR;
    }

    /**
     * Gets the border color based on widget state
     */
    protected int getBorderColor() {
        if (isPressed) return ACCENT_HOVER;
        if (isHovered) return ACCENT_COLOR;
        return BORDER_COLOR;
    }
    
    /**
     * Gets the text color based on widget state
     */
    protected int getTextColor() {
        return TEXT_COLOR;
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0 && isHovered) {
            isPressed = true;
            return true;
        }
        return false;
    }
    
    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0 && isPressed) {
            isPressed = false;
            if (isHovered) {
                onPress();
            }
            return true;
        }
        return false;
    }
    
    /**
     * Called when the widget is pressed
     */
    protected abstract void onPress();
    
    /**
     * Draws centered text with proper scaling
     */
    protected void drawCenteredText(DrawContext context, String text, int color) {
        var textRenderer = MinecraftClient.getInstance().textRenderer;
        int textWidth = textRenderer.getWidth(text);
        int textX = getX() + (getWidth() - textWidth) / 2;
        int textY = getY() + (getHeight() - textRenderer.fontHeight) / 2;
        context.drawText(textRenderer, text, textX, textY, color, false);
    }

    /**
     * Lightens a color by the given factor
     */
    protected int lightenColor(int color, float factor) {
        int r = (int) Math.min(255, ((color >> 16) & 0xFF) * factor);
        int g = (int) Math.min(255, ((color >> 8) & 0xFF) * factor);
        int b = (int) Math.min(255, (color & 0xFF) * factor);
        return (color & 0xFF000000) | (r << 16) | (g << 8) | b;
    }

    /**
     * Darkens a color by the given factor
     */
    protected int darkenColor(int color, float factor) {
        int r = (int) (((color >> 16) & 0xFF) * factor);
        int g = (int) (((color >> 8) & 0xFF) * factor);
        int b = (int) ((color & 0xFF) * factor);
        return (color & 0xFF000000) | (r << 16) | (g << 8) | b;
    }

    @Override
    protected void appendClickableNarrations(NarrationMessageBuilder builder) {
        builder.put(net.minecraft.client.gui.screen.narration.NarrationPart.TITLE, getMessage());
    }
}
