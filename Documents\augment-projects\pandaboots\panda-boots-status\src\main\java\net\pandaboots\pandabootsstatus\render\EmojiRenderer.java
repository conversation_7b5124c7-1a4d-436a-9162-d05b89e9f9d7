package net.pandaboots.pandabootsstatus.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.texture.NativeImageBackedTexture;
import net.minecraft.util.Identifier;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.status.MoodStatus;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.HashMap;
import java.util.Map;

/**
 * Renders emoji using simple colored squares as a fallback for proper emoji display
 */
public class EmojiRenderer {
    private static final Map<String, Identifier> emojiTextures = new HashMap<>();
    private static boolean initialized = false;
    
    // Emoji size in pixels
    private static final int EMOJI_SIZE = 12;
    
    // Color mappings for emoji fallbacks
    private static final int PANDA_COLOR = 0xFFFFFFFF;      // White for panda
    private static final int ANGRY_COLOR = 0xFFFF4444;      // Red for angry
    private static final int SLEEPY_COLOR = 0xFF4444FF;     // Blue for sleepy
    private static final int HAPPY_COLOR = 0xFF44FF44;      // Green for happy
    
    /**
     * Initializes the emoji rendering system
     */
    public static void initialize() {
        if (initialized) return;
        
        try {
            // Create simple colored square textures as emoji fallbacks
            createEmojiTexture("panda", PANDA_COLOR);
            createEmojiTexture("angry", ANGRY_COLOR);
            createEmojiTexture("sleepy", SLEEPY_COLOR);
            createEmojiTexture("happy", HAPPY_COLOR);
            
            initialized = true;
            PandaBootsStatus.LOGGER.info("Emoji renderer initialized with colored square fallbacks");
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to initialize emoji renderer", e);
            initialized = true; // Set to true to prevent repeated attempts
        }
    }
    
    /**
     * Creates a simple colored square texture for emoji fallback
     */
    private static void createEmojiTexture(String name, int color) {
        try {
            // Create a simple colored square
            NativeImage image = new NativeImage(EMOJI_SIZE, EMOJI_SIZE, false);
            
            // Fill with the specified color
            for (int x = 0; x < EMOJI_SIZE; x++) {
                for (int y = 0; y < EMOJI_SIZE; y++) {
                    // Create a simple pattern - filled square with border
                    if (x == 0 || y == 0 || x == EMOJI_SIZE - 1 || y == EMOJI_SIZE - 1) {
                        // Border - darker version of the color
                        int borderColor = darkenColor(color, 0.7f);
                        image.setColor(x, y, borderColor);
                    } else {
                        // Fill - original color
                        image.setColor(x, y, color);
                    }
                }
            }
            
            // Register the texture
            Identifier textureId = Identifier.of("panda-boots-status", "emoji/" + name);
            NativeImageBackedTexture texture = new NativeImageBackedTexture(image);
            MinecraftClient.getInstance().getTextureManager().registerTexture(textureId, texture);
            emojiTextures.put(name, textureId);
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to create emoji texture for: " + name, e);
        }
    }
    
    /**
     * Darkens a color by the given factor
     */
    private static int darkenColor(int color, float factor) {
        int a = (color >> 24) & 0xFF;
        int r = (int) (((color >> 16) & 0xFF) * factor);
        int g = (int) (((color >> 8) & 0xFF) * factor);
        int b = (int) ((color & 0xFF) * factor);
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
    
    /**
     * Renders an emoji at the specified position
     * @param context The draw context
     * @param emojiName The emoji name (panda, angry, sleepy, happy)
     * @param x X position
     * @param y Y position
     * @param scale Scale factor
     */
    public static void renderEmoji(DrawContext context, String emojiName, int x, int y, float scale) {
        if (!initialized) {
            initialize();
        }
        
        Identifier texture = emojiTextures.get(emojiName);
        if (texture != null) {
            int size = (int) (EMOJI_SIZE * scale);
            context.drawTexture(texture, x, y, 0, 0, size, size, size, size);
        } else {
            // Fallback to Unicode emoji if texture not found
            renderUnicodeEmoji(context, emojiName, x, y, scale);
        }
    }
    
    /**
     * Fallback to Unicode emoji rendering
     */
    private static void renderUnicodeEmoji(DrawContext context, String emojiName, int x, int y, float scale) {
        String unicodeEmoji = getUnicodeEmoji(emojiName);
        if (unicodeEmoji != null) {
            FontManager.drawText(context, unicodeEmoji, x, y, 0xFFFFFFFF, scale, false);
        }
    }
    
    /**
     * Gets the Unicode emoji for the given name
     */
    private static String getUnicodeEmoji(String emojiName) {
        switch (emojiName) {
            case "panda": return "🐼";
            case "angry": return "😡";
            case "sleepy": return "😴";
            case "happy": return "😄";
            default: return null;
        }
    }
    
    /**
     * Gets the emoji name for a mood status
     */
    public static String getEmojiName(MoodStatus status) {
        switch (status) {
            case AGGRESSIVE: return "angry";
            case LAZY: return "sleepy";
            case PLAYFUL: return "happy";
            default: return null;
        }
    }
    
    /**
     * Renders a mood emoji
     */
    public static void renderMoodEmoji(DrawContext context, MoodStatus status, int x, int y, float scale) {
        String emojiName = getEmojiName(status);
        if (emojiName != null) {
            renderEmoji(context, emojiName, x, y, scale);
        }
    }
    
    /**
     * Renders the panda emoji
     */
    public static void renderPandaEmoji(DrawContext context, int x, int y, float scale) {
        renderEmoji(context, "panda", x, y, scale);
    }
    
    /**
     * Gets the width of an emoji when rendered
     */
    public static int getEmojiWidth(float scale) {
        return (int) (EMOJI_SIZE * scale);
    }
    
    /**
     * Gets the height of an emoji when rendered
     */
    public static int getEmojiHeight(float scale) {
        return (int) (EMOJI_SIZE * scale);
    }
}
