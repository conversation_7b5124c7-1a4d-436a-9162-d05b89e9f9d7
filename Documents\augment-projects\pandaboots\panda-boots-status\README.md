# 🐼 Panda Boots Status

A modern Minecraft Fabric mod that displays Panda Boots mood swing status with a sleek visual interface and comprehensive customization options.

## ✨ Features

### 🆕 Enhanced Visual Status Display
- **Advanced Tier Detection**: Automatically detects "Mood Swings I-V" messages with tier parsing
- **Real-time Status Display**: Shows current mood status with tier, countdown timer, and effect descriptions
- **Modern GUI**: Comprehensive settings screen with live preview and extensive customization
- **Smart Positioning**: Advanced position/scale system with live preview and dragging
- **Rich Visual Elements**: Mood icons, tier-based effect calculations, and modern styling
- **Keybind Support**: Press 'O' key to open settings screen (configurable)

### 🎯 Mood Detection & Display
- **Automatic Detection**: Captures mood swing messages from action bar
- **Tier Support**: Handles all tiers (I through V) with different effect percentages
- **Single Status Display**: "Latest wins" logic - new mood swings replace existing status
- **10-Second Timer**: Each mood effect lasts exactly 10 seconds with countdown

### 🎨 Visual Customization
- **Position Control**: Advanced position screen with live preview and dragging
- **Scale Adjustment**: Resize from 50% to 200% with real-time preview
- **Background & Border**: Toggle both simultaneously with modern styling
- **Title Display**: Show/hide the "🐼 Panda Boots Status" title
- **Mood Icons**: Toggle mood emoji (😡😴😄) while preserving panda emoji
- **Effect Descriptions**: Show tier-based damage/speed calculations

## 🎮 Usage

### Installation
1. Download the latest release
2. Place `panda-boots-status-1.0.0.jar` in your `mods` folder
3. Launch Minecraft with Fabric Loader

### Configuration
- **Press 'O' key** to open the settings screen
- **Click "📍 Change Position & Scale"** for advanced positioning
- All settings are saved automatically

### Status Display Format
```
┌─────────────────────────────────┐
│ 🐼 Panda Boots Status           │
│ 😡 AGGRESSIVE V 8s              │
│ You are doing 30% more damage   │
└─────────────────────────────────┘
```

### Enhanced Features
- **Tier Detection**: Supports tiers I through V with different effect percentages
- **Effect Descriptions**: Tier-based damage/speed calculations
- **Mood Icons**: Apple emoji icons for each mood type
- **10-second countdown timer** for each status
- **Advanced positioning** with live preview and dragging
- **Comprehensive settings screen** (press 'O' key)
- **Automatic expiration** when timer reaches 0
- **Modern styling** with configurable background, borders, and transparency

## 🧪 Testing

### Method 1: Test Enhanced Mood Swing Messages (All Tiers)
```bash
# Test all tier levels
/title @s actionbar {"text":"Mood Swings I has made you feel lazy!","color":"blue"}
/title @s actionbar {"text":"Mood Swings II has made you feel aggressive!","color":"red"}
/title @s actionbar {"text":"Mood Swings III has made you feel playful!","color":"green"}
/title @s actionbar {"text":"Mood Swings IV has made you feel lazy!","color":"blue"}
/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!","color":"red"}

# Test effect descriptions
/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!","color":"red"}
# Should show: "You are doing 30% more damage"

/title @s actionbar {"text":"Mood Swings I has made you feel lazy!","color":"blue"}
# Should show: "You are moving 28% slower"
```

### Method 2: Test Single Status Display
```bash
# Test rapid replacement (latest wins)
/title @s actionbar {"text":"Mood Swings IV has made you feel lazy!"}
/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!"}
# Second command should immediately replace the first
```

### Method 3: Test Settings and Positioning
1. **Open settings**: Press 'O' key
2. **Test toggles**: Try "Show Title", "Show Mood Icons", "Toggle Background & Border"
3. **Test positioning**: Click "📍 Change Position & Scale"
4. **Test dragging**: Click and drag the preview status box
5. **Test scaling**: Use scale buttons (-, +, 100%, 80%)

## 🔧 Effect Calculations

### Tier-Based Effects
- **Aggressive**: "You are doing X% more damage"
  - Formula: 5% base + (5% × tier level) = I:10%, II:15%, III:20%, IV:25%, V:30%
- **Playful**: "You are moving X% faster"
  - Formula: 5% base + (5% × tier level) = I:10%, II:15%, III:20%, IV:25%, V:30%
- **Lazy**: "You are moving X% slower"
  - Formula: 30% base - (2% × tier level) = I:28%, II:26%, III:24%, IV:22%, V:20%

## 📋 Requirements

- **Minecraft**: 1.21.1
- **Fabric Loader**: 0.16.12+
- **Fabric API**: Required
- **Java**: 21+

## 🔄 Migration from Action Bar Logger

If you're upgrading from the old "Action Bar Logger" mod:
- Your settings will be automatically migrated
- Configuration file will be updated from `actionbar-logger.json` to `panda-boots-status.json`
- Log files will be moved to the new `panda-boots-status/` directory
- All customizations will be preserved

## 🎯 Key Features Summary

✅ **Single Status Display** - "Latest wins" logic with immediate replacement  
✅ **Advanced Positioning** - Live preview with drag-and-drop positioning  
✅ **Tier Detection** - Full support for Mood Swings I-V with effect calculations  
✅ **Modern GUI** - Comprehensive settings with readwork-inspired design  
✅ **Emoji Support** - Proper Apple emoji implementation with selective toggling  
✅ **Background Control** - Toggle background and border simultaneously  
✅ **Effect Descriptions** - Tier-based damage/speed percentage display  
✅ **Backward Compatible** - Seamless migration from Action Bar Logger  

## 👨‍💻 Author

**Cerv** - Complete rebranding and enhancement of the original Action Bar Logger mod

## 📄 License

MIT License - Feel free to modify and distribute

---

**Enjoy your enhanced Panda Boots experience with beautiful, modern status displays!** 🐼✨
