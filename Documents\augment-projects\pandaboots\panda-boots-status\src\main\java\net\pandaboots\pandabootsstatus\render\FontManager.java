package net.pandaboots.pandabootsstatus.render;

import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import org.slf4j.Logger;

/**
 * Manages custom font rendering for the mod
 * Adapted from readwork mod's FontManager
 */
public class FontManager {
    private static final Logger LOGGER = PandaBootsStatus.LOGGER;
    
    private static boolean fontInitialized = false;

    /**
     * Initializes the font system
     */
    public static void initialize() {
        if (fontInitialized) return;
        
        try {
            fontInitialized = true;
            LOGGER.info("Font system initialized");
            
        } catch (Exception e) {
            LOGGER.error("Failed to initialize custom font, falling back to default", e);
            fontInitialized = true;
        }
    }

    /**
     * Renders text using the custom font with improved stability
     * @param context The draw context
     * @param text The text to render
     * @param x X position
     * @param y Y position
     * @param color Text color
     * @param scale Text scale
     * @param shadow Whether to draw shadow
     */
    public static void drawText(DrawContext context, String text, int x, int y, int color, float scale, boolean shadow) {
        if (!fontInitialized) {
            initialize();
        }

        // Validate inputs to prevent rendering issues
        if (text == null || text.isEmpty()) return;
        if (scale <= 0.0f) scale = 1.0f;

        MinecraftClient client = MinecraftClient.getInstance();
        TextRenderer textRenderer = client.textRenderer;

        // Use matrix transformations for stable scaling
        context.getMatrices().push();

        // Apply scaling with proper rounding to prevent sub-pixel jitter
        context.getMatrices().scale(scale, scale, 1.0f);

        // Calculate scaled positions with proper rounding
        int scaledX = Math.round(x / scale);
        int scaledY = Math.round(y / scale);

        // Ensure color has proper alpha channel
        if ((color & 0xFF000000) == 0) {
            color |= 0xFF000000; // Add full alpha if not specified
        }

        try {
            if (shadow) {
                context.drawText(textRenderer, text, scaledX, scaledY, color, true);
            } else {
                context.drawText(textRenderer, text, scaledX, scaledY, color, false);
            }
        } catch (Exception e) {
            LOGGER.error("Error rendering text: {}", text, e);
        }

        context.getMatrices().pop();
    }

    /**
     * Gets the width of text when rendered with the custom font
     * @param text The text to measure
     * @param scale The scale factor
     * @return The width in pixels
     */
    public static int getTextWidth(String text, float scale) {
        if (!fontInitialized) {
            initialize();
        }
        
        // For now, use Minecraft's text renderer for measurements
        MinecraftClient client = MinecraftClient.getInstance();
        TextRenderer textRenderer = client.textRenderer;
        
        return (int) (textRenderer.getWidth(text) * scale);
    }

    /**
     * Gets the height of text when rendered with the custom font
     * @param scale The scale factor
     * @return The height in pixels
     */
    public static int getTextHeight(float scale) {
        if (!fontInitialized) {
            initialize();
        }
        
        // For now, use Minecraft's text renderer for measurements
        MinecraftClient client = MinecraftClient.getInstance();
        TextRenderer textRenderer = client.textRenderer;
        
        return (int) (textRenderer.fontHeight * scale);
    }
}
