package net.pandaboots.pandabootsstatus.gui;

import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.config.ActionBarConfig;
import net.pandaboots.pandabootsstatus.render.FontManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import org.slf4j.Logger;

/**
 * Screen for adjusting status position with live preview
 * Based on readwork's TextPositionScreen
 */
public class StatusPositionScreen extends Screen {
    private static final Logger LOGGER = PandaBootsStatus.LOGGER;
    
    private final Screen parent;
    private final ActionBarConfig config;
    
    // Dragging state
    private boolean isDragging = false;
    private int dragStartX, dragStartY;
    private int statusStartX, statusStartY;
    
    // Preview text - simulate status display
    private static final String[] PREVIEW_TEXTS = {
        "🐼 Panda Boots Status",
        "😠 AGGRESSIVE V 8s",
        "▓▓▓▓▓▓▓▓░░ 80%",
        "You are doing 25% more damage"
    };
    
    // UI Constants
    private static final int BUTTON_HEIGHT = 20;
    private static final int BACKGROUND_COLOR = 0x80000000;
    private static final int TEXT_COLOR = 0xFFFFFFFF;
    private static final int PREVIEW_COLOR = 0xFF0000; // Red for preview
    
    public StatusPositionScreen(Screen parent) {
        super(Text.literal("Adjust Status Position"));
        this.parent = parent;
        this.config = ActionBarConfig.getInstance();
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Control buttons
        int buttonY = height - 80;
        int buttonWidth = 100;
        int buttonSpacing = 110;
        int startX = (width - (buttonSpacing * 4)) / 2;
        
        // Fine adjustment buttons
        addDrawableChild(ButtonWidget.builder(Text.literal("← Left"), button -> {
            int currentX = config.getStatusX();
            if (currentX == -1) currentX = width / 2;
            config.setStatusX(Math.max(0, currentX - 5));
        }).dimensions(startX, buttonY, buttonWidth, BUTTON_HEIGHT).build());
        
        addDrawableChild(ButtonWidget.builder(Text.literal("Right →"), button -> {
            int currentX = config.getStatusX();
            if (currentX == -1) currentX = width / 2;
            config.setStatusX(Math.min(width, currentX + 5));
        }).dimensions(startX + buttonSpacing, buttonY, buttonWidth, BUTTON_HEIGHT).build());
        
        addDrawableChild(ButtonWidget.builder(Text.literal("↑ Up"), button -> {
            config.setStatusY(Math.max(0, config.getStatusY() - 5));
        }).dimensions(startX + buttonSpacing * 2, buttonY, buttonWidth, BUTTON_HEIGHT).build());
        
        addDrawableChild(ButtonWidget.builder(Text.literal("Down ↓"), button -> {
            config.setStatusY(Math.min(height, config.getStatusY() + 5));
        }).dimensions(startX + buttonSpacing * 3, buttonY, buttonWidth, BUTTON_HEIGHT).build());
        
        // Preset position buttons
        buttonY += 30;
        
        addDrawableChild(ButtonWidget.builder(Text.literal("Top Center"), button -> {
            config.setStatusX(-1); // Center
            config.setStatusY(20);
        }).dimensions(startX, buttonY, buttonWidth, BUTTON_HEIGHT).build());
        
        addDrawableChild(ButtonWidget.builder(Text.literal("Center"), button -> {
            config.setStatusX(-1); // Center
            config.setStatusY(height / 2);
        }).dimensions(startX + buttonSpacing, buttonY, buttonWidth, BUTTON_HEIGHT).build());
        
        addDrawableChild(ButtonWidget.builder(Text.literal("Bottom"), button -> {
            config.setStatusX(-1); // Center
            config.setStatusY(height - 100);
        }).dimensions(startX + buttonSpacing * 2, buttonY, buttonWidth, BUTTON_HEIGHT).build());
        
        addDrawableChild(ButtonWidget.builder(Text.literal("Reset"), button -> {
            config.setStatusX(-1);
            config.setStatusY(50);
            config.setStatusScale(1.0f);
        }).dimensions(startX + buttonSpacing * 3, buttonY, buttonWidth, BUTTON_HEIGHT).build());

        // Scale adjustment buttons
        buttonY += 30;

        addDrawableChild(ButtonWidget.builder(Text.literal("Scale -"), button -> {
            float currentScale = config.getStatusScale();
            config.setStatusScale(Math.max(0.5f, currentScale - 0.1f));
        }).dimensions(startX, buttonY, buttonWidth, BUTTON_HEIGHT).build());

        addDrawableChild(ButtonWidget.builder(Text.literal("Scale +"), button -> {
            float currentScale = config.getStatusScale();
            config.setStatusScale(Math.min(2.0f, currentScale + 0.1f));
        }).dimensions(startX + buttonSpacing, buttonY, buttonWidth, BUTTON_HEIGHT).build());

        addDrawableChild(ButtonWidget.builder(Text.literal("Scale 100%"), button -> {
            config.setStatusScale(1.0f);
        }).dimensions(startX + buttonSpacing * 2, buttonY, buttonWidth, BUTTON_HEIGHT).build());

        addDrawableChild(ButtonWidget.builder(Text.literal("Scale 80%"), button -> {
            config.setStatusScale(0.8f);
        }).dimensions(startX + buttonSpacing * 3, buttonY, buttonWidth, BUTTON_HEIGHT).build());

        // Bottom control buttons
        buttonY = height - 30;
        
        addDrawableChild(ButtonWidget.builder(Text.literal("Done"), button -> {
            close();
        }).dimensions(width - 90, buttonY, 80, BUTTON_HEIGHT).build());
        
        addDrawableChild(ButtonWidget.builder(Text.literal("Cancel"), button -> {
            close();
        }).dimensions(width - 180, buttonY, 80, BUTTON_HEIGHT).build());
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Check if clicking on the preview box
            int[] boxBounds = getPreviewBoxBounds();
            int boxX = boxBounds[0];
            int boxY = boxBounds[1];
            int boxWidth = boxBounds[2];
            int boxHeight = boxBounds[3];

            if (mouseX >= boxX && mouseX <= boxX + boxWidth &&
                mouseY >= boxY && mouseY <= boxY + boxHeight) {

                isDragging = true;
                dragStartX = (int) mouseX;
                dragStartY = (int) mouseY;
                statusStartX = boxX;
                statusStartY = boxY;
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0 && isDragging) {
            isDragging = false;
            return true;
        }
        
        return super.mouseReleased(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDragging) {
            int newX = statusStartX + ((int) mouseX - dragStartX);
            int newY = statusStartY + ((int) mouseY - dragStartY);

            // Get box bounds for clamping
            int[] boxBounds = getPreviewBoxBounds();
            int boxWidth = boxBounds[2];
            int boxHeight = boxBounds[3];

            // Clamp to screen bounds
            newX = Math.max(0, Math.min(width - boxWidth, newX));
            newY = Math.max(0, Math.min(height - boxHeight, newY));

            // Update config
            config.setStatusX(newX);
            config.setStatusY(newY);

            return true;
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    /**
     * Gets the preview box bounds
     * @return int array [x, y, width, height]
     */
    private int[] getPreviewBoxBounds() {
        float scale = config.getStatusScale();
        int padding = 8;
        int lineHeight = (int) (FontManager.getTextHeight(scale) + 2);

        // Find the widest text to determine box width
        int maxWidth = 0;
        for (String text : PREVIEW_TEXTS) {
            int textWidth = FontManager.getTextWidth(text, scale);
            maxWidth = Math.max(maxWidth, textWidth);
        }

        int boxWidth = maxWidth + (padding * 2);
        int boxHeight = (PREVIEW_TEXTS.length * lineHeight) + (padding * 2);

        // Get box position
        int boxX = config.getStatusX();
        int boxY = config.getStatusY();

        // Center horizontally if x is -1
        if (boxX == -1) {
            boxX = (width / 2) - (boxWidth / 2);
        }

        return new int[]{boxX, boxY, boxWidth, boxHeight};
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw semi-transparent background
        context.fill(0, 0, width, height, BACKGROUND_COLOR);
        
        super.render(context, mouseX, mouseY, delta);
        
        // Draw title
        context.drawCenteredTextWithShadow(textRenderer, title, width / 2, 5, TEXT_COLOR);
        
        // Draw instructions
        String[] instructions = {
            "Click and drag the preview status to position it",
            "Use the buttons below for fine adjustments",
            "Current position: X=" + config.getStatusX() + ", Y=" + config.getStatusY(),
            "Scale: " + (int)(config.getStatusScale() * 100) + "%"
        };
        
        for (int i = 0; i < instructions.length; i++) {
            context.drawCenteredTextWithShadow(textRenderer, instructions[i], width / 2, 25 + i * 12, TEXT_COLOR);
        }
        
        // Draw preview box
        int[] boxBounds = getPreviewBoxBounds();
        int boxX = boxBounds[0];
        int boxY = boxBounds[1];
        int boxWidth = boxBounds[2];
        int boxHeight = boxBounds[3];
        float scale = config.getStatusScale();

        // Draw box background and border (same as in StatusRenderer)
        int backgroundColor = 0x80000000; // Semi-transparent black
        int borderColor = 0xFF444444; // Dark gray border

        // Background
        context.fill(boxX, boxY, boxX + boxWidth, boxY + boxHeight, backgroundColor);

        // Border
        context.drawBorder(boxX, boxY, boxWidth, boxHeight, borderColor);

        // Draw preview texts
        int padding = 8;
        int lineHeight = (int) (FontManager.getTextHeight(scale) + 2);
        int currentY = boxY + padding;

        int[] colors = {0xFFFFFF, 0xE74C3C, 0x3B82F6, 0x888888}; // White, Red, Blue, Gray

        for (int i = 0; i < PREVIEW_TEXTS.length; i++) {
            String text = PREVIEW_TEXTS[i];
            int color = colors[i];

            // Draw the preview text using our custom font manager
            FontManager.drawText(context, text, boxX + padding, currentY, color, scale, false);

            currentY += lineHeight;
        }
        
        // Draw crosshair at mouse position when dragging
        if (isDragging) {
            context.drawHorizontalLine((int)mouseX - 10, (int)mouseX + 10, (int)mouseY, 0xFFFFFFFF);
            context.drawVerticalLine((int)mouseX, (int)mouseY - 10, (int)mouseY + 10, 0xFFFFFFFF);
        }
        
        // Draw grid lines for alignment (subtle)
        int gridColor = 0x20FFFFFF;
        for (int x = 0; x < width; x += 50) {
            context.drawVerticalLine(x, 0, height, gridColor);
        }
        for (int y = 0; y < height; y += 50) {
            context.drawHorizontalLine(0, width, y, gridColor);
        }
        
        // Draw center lines
        context.drawVerticalLine(width / 2, 0, height, 0x40FF0000);
        context.drawHorizontalLine(0, width, height / 2, 0x40FF0000);
    }
    
    @Override
    public void close() {
        client.setScreen(parent);
    }
}
