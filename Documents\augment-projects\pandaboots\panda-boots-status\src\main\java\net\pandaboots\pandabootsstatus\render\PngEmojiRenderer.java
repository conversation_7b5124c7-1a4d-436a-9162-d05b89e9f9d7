package net.pandaboots.pandabootsstatus.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.texture.NativeImageBackedTexture;
import net.minecraft.util.Identifier;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.status.MoodStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * PNG-based emoji renderer with fallback to simple colored shapes
 */
public class PngEmojiRenderer {
    private static final Map<String, Identifier> emojiTextures = new HashMap<>();
    private static boolean initialized = false;
    
    // Emoji size in pixels - half size for subtle emojis
    private static final int EMOJI_SIZE = 8;   // Half size from original 16 to 8
    
    // Color mappings for emoji fallbacks
    private static final int PANDA_COLOR = 0xFF000000;      // Black and white for panda
    private static final int PANDA_ACCENT = 0xFFFFFFFF;     // White accent
    private static final int ANGRY_COLOR = 0xFFFF3333;      // Red for angry
    private static final int SLEEPY_COLOR = 0xFF3366FF;     // Blue for sleepy  
    private static final int HAPPY_COLOR = 0xFF33FF33;      // Green for happy
    
    /**
     * Initializes the PNG emoji rendering system
     */
    public static void initialize() {
        if (initialized) return;

        try {
            // Check if we're on the render thread and OpenGL context is available
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null) {
                PandaBootsStatus.LOGGER.warn("Minecraft client not available, deferring emoji initialization");
                return;
            }

            // Defer initialization until we're sure OpenGL is ready
            if (!client.isOnThread()) {
                PandaBootsStatus.LOGGER.info("Deferring PNG emoji initialization to render thread");
                client.execute(() -> initialize());
                return;
            }

            PandaBootsStatus.LOGGER.info("Initializing PNG emoji renderer...");

            // Create PNG-style emoji textures with error handling
            safeCreateEmoji("panda", PngEmojiRenderer::createPandaEmoji);
            safeCreateEmoji("angry", PngEmojiRenderer::createAngryEmoji);
            safeCreateEmoji("sleepy", PngEmojiRenderer::createSleepyEmoji);
            safeCreateEmoji("happy", PngEmojiRenderer::createHappyEmoji);

            initialized = true;
            PandaBootsStatus.LOGGER.info("PNG emoji renderer initialized successfully with {} textures", emojiTextures.size());

        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to initialize PNG emoji renderer, falling back to Unicode", e);
            initialized = true; // Set to true to prevent repeated attempts
        }
    }

    /**
     * Safely creates an emoji with error handling
     */
    private static void safeCreateEmoji(String name, Runnable creator) {
        try {
            creator.run();
            PandaBootsStatus.LOGGER.debug("Successfully created {} emoji texture", name);
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.warn("Failed to create {} emoji texture: {}", name, e.getMessage());
        }
    }
    
    /**
     * Creates a panda emoji texture (black and white pattern)
     */
    private static void createPandaEmoji() {
        try {
            NativeImage image = new NativeImage(EMOJI_SIZE, EMOJI_SIZE, false);
            
            // Create a simple panda face pattern
            for (int x = 0; x < EMOJI_SIZE; x++) {
                for (int y = 0; y < EMOJI_SIZE; y++) {
                    int color = 0xFFFFFFFF; // Default white
                    
                    // Create panda pattern
                    int centerX = EMOJI_SIZE / 2;
                    int centerY = EMOJI_SIZE / 2;
                    int distFromCenter = (int) Math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    
                    // Outer circle (head)
                    if (distFromCenter <= 7) {
                        color = 0xFFFFFFFF; // White face
                        
                        // Eyes (black dots)
                        if ((x == centerX - 2 && y == centerY - 1) || (x == centerX + 2 && y == centerY - 1)) {
                            color = 0xFF000000;
                        }
                        // Nose (small black dot)
                        if (x == centerX && y == centerY + 1) {
                            color = 0xFF000000;
                        }
                    }
                    
                    // Ears (black circles)
                    int leftEarX = centerX - 4, leftEarY = centerY - 4;
                    int rightEarX = centerX + 4, rightEarY = centerY - 4;
                    
                    if (Math.sqrt((x - leftEarX) * (x - leftEarX) + (y - leftEarY) * (y - leftEarY)) <= 2) {
                        color = 0xFF000000;
                    }
                    if (Math.sqrt((x - rightEarX) * (x - rightEarX) + (y - rightEarY) * (y - rightEarY)) <= 2) {
                        color = 0xFF000000;
                    }
                    
                    // Border
                    if (x == 0 || y == 0 || x == EMOJI_SIZE - 1 || y == EMOJI_SIZE - 1) {
                        color = 0xFF888888;
                    }
                    
                    image.setColor(x, y, color);
                }
            }
            
            registerTexture("panda", image);
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to create panda emoji", e);
        }
    }
    
    /**
     * Creates an angry emoji texture (red with angry expression)
     */
    private static void createAngryEmoji() {
        try {
            NativeImage image = new NativeImage(EMOJI_SIZE, EMOJI_SIZE, false);
            
            for (int x = 0; x < EMOJI_SIZE; x++) {
                for (int y = 0; y < EMOJI_SIZE; y++) {
                    int color = 0x00000000; // Transparent
                    
                    int centerX = EMOJI_SIZE / 2;
                    int centerY = EMOJI_SIZE / 2;
                    int distFromCenter = (int) Math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    
                    // Main circle (angry red face)
                    if (distFromCenter <= 7) {
                        color = ANGRY_COLOR;
                        
                        // Angry eyes (black slanted lines)
                        if ((x == centerX - 2 && y == centerY - 2) || (x == centerX - 1 && y == centerY - 1) ||
                            (x == centerX + 2 && y == centerY - 2) || (x == centerX + 1 && y == centerY - 1)) {
                            color = 0xFF000000;
                        }
                        
                        // Angry mouth (downward curve)
                        if (y == centerY + 2 && (x >= centerX - 2 && x <= centerX + 2)) {
                            color = 0xFF000000;
                        }
                        if (y == centerY + 3 && (x == centerX - 1 || x == centerX + 1)) {
                            color = 0xFF000000;
                        }
                    }
                    
                    // Border
                    if (distFromCenter == 7) {
                        color = darkenColor(ANGRY_COLOR, 0.7f);
                    }
                    
                    image.setColor(x, y, color);
                }
            }
            
            registerTexture("angry", image);
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to create angry emoji", e);
        }
    }
    
    /**
     * Creates a sleepy emoji texture (blue with sleepy expression)
     */
    private static void createSleepyEmoji() {
        try {
            NativeImage image = new NativeImage(EMOJI_SIZE, EMOJI_SIZE, false);
            
            for (int x = 0; x < EMOJI_SIZE; x++) {
                for (int y = 0; y < EMOJI_SIZE; y++) {
                    int color = 0x00000000; // Transparent
                    
                    int centerX = EMOJI_SIZE / 2;
                    int centerY = EMOJI_SIZE / 2;
                    int distFromCenter = (int) Math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    
                    // Main circle (sleepy blue face)
                    if (distFromCenter <= 7) {
                        color = SLEEPY_COLOR;
                        
                        // Sleepy eyes (horizontal lines)
                        if (y == centerY - 1 && (x >= centerX - 3 && x <= centerX - 1)) {
                            color = 0xFF000000;
                        }
                        if (y == centerY - 1 && (x >= centerX + 1 && x <= centerX + 3)) {
                            color = 0xFF000000;
                        }
                        
                        // Small mouth
                        if (x == centerX && y == centerY + 2) {
                            color = 0xFF000000;
                        }
                    }
                    
                    // Border
                    if (distFromCenter == 7) {
                        color = darkenColor(SLEEPY_COLOR, 0.7f);
                    }
                    
                    // Z's for sleeping effect
                    if ((x == centerX + 6 && y == centerY - 6) || 
                        (x == centerX + 7 && y == centerY - 5) ||
                        (x == centerX + 8 && y == centerY - 4)) {
                        color = 0xFF000000;
                    }
                    
                    image.setColor(x, y, color);
                }
            }
            
            registerTexture("sleepy", image);
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to create sleepy emoji", e);
        }
    }
    
    /**
     * Creates a happy emoji texture (green with happy expression)
     */
    private static void createHappyEmoji() {
        try {
            NativeImage image = new NativeImage(EMOJI_SIZE, EMOJI_SIZE, false);
            
            for (int x = 0; x < EMOJI_SIZE; x++) {
                for (int y = 0; y < EMOJI_SIZE; y++) {
                    int color = 0x00000000; // Transparent
                    
                    int centerX = EMOJI_SIZE / 2;
                    int centerY = EMOJI_SIZE / 2;
                    int distFromCenter = (int) Math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    
                    // Main circle (happy green face)
                    if (distFromCenter <= 7) {
                        color = HAPPY_COLOR;
                        
                        // Happy eyes (dots)
                        if ((x == centerX - 2 && y == centerY - 1) || (x == centerX + 2 && y == centerY - 1)) {
                            color = 0xFF000000;
                        }
                        
                        // Happy smile (upward curve)
                        if (y == centerY + 1 && (x >= centerX - 2 && x <= centerX + 2)) {
                            color = 0xFF000000;
                        }
                        if (y == centerY + 2 && (x == centerX - 3 || x == centerX + 3)) {
                            color = 0xFF000000;
                        }
                    }
                    
                    // Border
                    if (distFromCenter == 7) {
                        color = darkenColor(HAPPY_COLOR, 0.7f);
                    }
                    
                    image.setColor(x, y, color);
                }
            }
            
            registerTexture("happy", image);
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to create happy emoji", e);
        }
    }
    
    /**
     * Registers a texture with Minecraft's texture manager
     */
    private static void registerTexture(String name, NativeImage image) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null || client.getTextureManager() == null) {
                PandaBootsStatus.LOGGER.warn("Cannot register texture {}: texture manager not available", name);
                if (image != null) {
                    image.close(); // Clean up the image
                }
                return;
            }

            Identifier textureId = Identifier.of("panda-boots-status", "emoji/" + name);
            NativeImageBackedTexture texture = new NativeImageBackedTexture(image);

            // Register on the render thread
            if (client.isOnThread()) {
                client.getTextureManager().registerTexture(textureId, texture);
                emojiTextures.put(name, textureId);
                PandaBootsStatus.LOGGER.debug("Registered texture: {}", textureId);
            } else {
                client.execute(() -> {
                    try {
                        client.getTextureManager().registerTexture(textureId, texture);
                        emojiTextures.put(name, textureId);
                        PandaBootsStatus.LOGGER.debug("Registered texture: {}", textureId);
                    } catch (Exception e) {
                        PandaBootsStatus.LOGGER.error("Failed to register texture {} on render thread", name, e);
                        texture.close(); // Clean up on failure
                    }
                });
            }

        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to register texture for: " + name, e);
            if (image != null) {
                try {
                    image.close(); // Clean up the image on error
                } catch (Exception closeError) {
                    PandaBootsStatus.LOGGER.warn("Failed to close image for {}: {}", name, closeError.getMessage());
                }
            }
        }
    }
    
    /**
     * Darkens a color by the given factor
     */
    private static int darkenColor(int color, float factor) {
        int a = (color >> 24) & 0xFF;
        int r = (int) (((color >> 16) & 0xFF) * factor);
        int g = (int) (((color >> 8) & 0xFF) * factor);
        int b = (int) ((color & 0xFF) * factor);
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
    
    /**
     * Renders an emoji at the specified position
     */
    public static void renderEmoji(DrawContext context, String emojiName, int x, int y, float scale) {
        if (!initialized) {
            initialize();
        }
        
        Identifier texture = emojiTextures.get(emojiName);
        if (texture != null) {
            int size = (int) (EMOJI_SIZE * scale);
            context.drawTexture(texture, x, y, 0, 0, size, size, size, size);
        } else {
            // Fallback to Unicode emoji
            renderUnicodeEmoji(context, emojiName, x, y, scale);
        }
    }
    
    /**
     * Fallback to Unicode emoji rendering
     */
    private static void renderUnicodeEmoji(DrawContext context, String emojiName, int x, int y, float scale) {
        String unicodeEmoji = getUnicodeEmoji(emojiName);
        if (unicodeEmoji != null) {
            FontManager.drawText(context, unicodeEmoji, x, y, 0xFFFFFFFF, scale, false);
        }
    }
    
    /**
     * Gets the Unicode emoji for the given name
     */
    private static String getUnicodeEmoji(String emojiName) {
        switch (emojiName) {
            case "panda": return "🐼";
            case "angry": return "😡";
            case "sleepy": return "😴";
            case "happy": return "😄";
            default: return null;
        }
    }
    
    /**
     * Gets the emoji name for a mood status
     */
    public static String getEmojiName(MoodStatus status) {
        switch (status) {
            case AGGRESSIVE: return "angry";
            case LAZY: return "sleepy";
            case PLAYFUL: return "happy";
            default: return null;
        }
    }
    
    /**
     * Renders a mood emoji
     */
    public static void renderMoodEmoji(DrawContext context, MoodStatus status, int x, int y, float scale) {
        String emojiName = getEmojiName(status);
        if (emojiName != null) {
            renderEmoji(context, emojiName, x, y, scale);
        }
    }
    
    /**
     * Renders the panda emoji
     */
    public static void renderPandaEmoji(DrawContext context, int x, int y, float scale) {
        renderEmoji(context, "panda", x, y, scale);
    }
    
    /**
     * Gets the width of an emoji when rendered
     */
    public static int getEmojiWidth(float scale) {
        return (int) (EMOJI_SIZE * scale);
    }
    
    /**
     * Gets the height of an emoji when rendered
     */
    public static int getEmojiHeight(float scale) {
        return (int) (EMOJI_SIZE * scale);
    }
}
