package net.pandaboots.pandabootsstatus.logger;

import net.fabricmc.loader.api.FabricLoader;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.config.ActionBarConfig;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

public class MessageLogger {
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final BlockingQueue<String> messageQueue = new LinkedBlockingQueue<>();
    private static final AtomicBoolean running = new AtomicBoolean(false);
    private static Thread loggerThread;

    public static void initialize() {
        if (running.compareAndSet(false, true)) {
            loggerThread = new Thread(MessageLogger::processMessages, "ActionBar-Logger");
            loggerThread.setDaemon(true);
            loggerThread.start();
            PandaBootsStatus.LOGGER.info("Message logger initialized");
        }
    }

    public static void shutdown() {
        if (running.compareAndSet(true, false)) {
            if (loggerThread != null) {
                loggerThread.interrupt();
                try {
                    loggerThread.join(1000); // Wait up to 1 second
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            PandaBootsStatus.LOGGER.info("Message logger shutdown");
        }
    }

    public static void logMessage(String message) {
        ActionBarConfig config = ActionBarConfig.getInstance();
        if (!config.isEnabled()) {
            return;
        }

        try {
            messageQueue.offer(message);
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to queue message for logging", e);
        }
    }

    private static void processMessages() {
        while (running.get() && !Thread.currentThread().isInterrupted()) {
            try {
                String message = messageQueue.take();
                writeMessageToFile(message);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                PandaBootsStatus.LOGGER.error("Error processing message", e);
            }
        }

        // Process remaining messages
        while (!messageQueue.isEmpty()) {
            try {
                String message = messageQueue.poll();
                if (message != null) {
                    writeMessageToFile(message);
                }
            } catch (Exception e) {
                PandaBootsStatus.LOGGER.error("Error processing remaining message", e);
            }
        }
    }

    private static void writeMessageToFile(String message) {
        ActionBarConfig config = ActionBarConfig.getInstance();
        
        try {
            Path logPath = resolveLogPath(config.getLogFilePath());
            
            // Create directories if needed
            if (config.isCreateDirectories()) {
                Files.createDirectories(logPath.getParent());
            }

            // Check file size and rotate if necessary
            if (config.isRotateLogFiles() && Files.exists(logPath)) {
                long fileSizeBytes = Files.size(logPath);
                long maxSizeBytes = config.getMaxLogFileSize() * 1024L * 1024L; // Convert MB to bytes
                
                if (fileSizeBytes > maxSizeBytes) {
                    rotateLogFile(logPath);
                }
            }

            // Format the log entry
            String logEntry = formatLogEntry(message, config);

            // Write to file
            try (BufferedWriter writer = Files.newBufferedWriter(logPath, 
                    StandardOpenOption.CREATE, StandardOpenOption.APPEND)) {
                writer.write(logEntry);
                writer.newLine();
                writer.flush();
            }

        } catch (IOException e) {
            PandaBootsStatus.LOGGER.error("Failed to write message to log file: {}", message, e);
        }
    }

    private static Path resolveLogPath(String logFilePath) {
        Path path = Paths.get(logFilePath);
        if (path.isAbsolute()) {
            return path;
        } else {
            // Relative to game directory
            return FabricLoader.getInstance().getGameDir().resolve(logFilePath);
        }
    }

    private static String formatLogEntry(String message, ActionBarConfig config) {
        if (config.isIncludeTimestamp()) {
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
            return String.format("[%s] Action Bar: %s", timestamp, message);
        } else {
            return String.format("Action Bar: %s", message);
        }
    }

    private static void rotateLogFile(Path logPath) throws IOException {
        String fileName = logPath.getFileName().toString();
        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
        String extension = fileName.substring(fileName.lastIndexOf('.'));
        
        Path rotatedPath = logPath.getParent().resolve(baseName + "_" + 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + extension);
        
        Files.move(logPath, rotatedPath);
        PandaBootsStatus.LOGGER.info("Rotated log file to: {}", rotatedPath);
    }
}
