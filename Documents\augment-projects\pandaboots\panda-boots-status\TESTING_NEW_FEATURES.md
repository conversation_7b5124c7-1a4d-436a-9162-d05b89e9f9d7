# 🧪 Testing Guide - New Features

## 🎯 **Testing External PNG Emoji System**

### **Test 1: Default Behavior (No Custom PNGs)**
1. **Install mod** without adding custom PNG files
2. **Trigger mood swing**: `/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!"}`
3. **Expected**: Should use generated textures or Unicode emojis as fallback
4. **Check logs**: Should show "No external PNG emoji files found"

### **Test 2: Custom PNG Files**
1. **Create custom emoji PNGs** (16x16 pixels recommended):
   - `aggressive.png` - Red angry face
   - `playful.png` - Green happy face  
   - `lazy.png` - Blue sleepy face
   - `panda.png` - Black and white panda face

2. **Place files** in: `src/main/resources/assets/panda-boots-status/textures/emoji/`

3. **Rebuild mod**: `.\gradlew.bat build`

4. **Test each emoji**:
   ```bash
   /title @s actionbar {"text":"Mood Swings V has made you feel aggressive!"}
   /title @s actionbar {"text":"Mood Swings V has made you feel lazy!"}
   /title @s actionbar {"text":"Mood Swings V has made you feel playful!"}
   ```

5. **Expected**: Should display your custom PNG emojis

6. **Check logs**: Should show "External PNG emoji files are available"

### **Test 3: Partial PNG Files**
1. **Add only some PNG files** (e.g., just `aggressive.png`)
2. **Test all moods**
3. **Expected**: Custom PNG for aggressive, fallback for others

---

## 🎯 **Testing "Show Mood Icons" Toggle Fix**

### **Test 1: Toggle Behavior**
1. **Press 'O'** to open settings
2. **Ensure "🐼 Show Title" is ON**
3. **Ensure "😄 Show Mood Icons" is ON**
4. **Trigger mood**: `/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!"}`
5. **Expected Display**:
   ```
   ┌─────────────────────────────────┐
   │ [🐼] Panda Boots Status         │  ← Panda emoji visible
   │ [😡] AGGRESSIVE V 8s            │  ← Mood emoji visible
   │ You are doing 30% more damage   │
   └─────────────────────────────────┘
   ```

### **Test 2: Hide Mood Icons Only**
1. **Toggle "😄 Show Mood Icons" OFF**
2. **Trigger mood**: `/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!"}`
3. **Expected Display**:
   ```
   ┌─────────────────────────────────┐
   │ [🐼] Panda Boots Status         │  ← Panda emoji STILL visible
   │ AGGRESSIVE V 8s                 │  ← Mood emoji HIDDEN
   │ You are doing 30% more damage   │
   └─────────────────────────────────┘
   ```

### **Test 3: Hide Title (Including Panda)**
1. **Toggle "🐼 Show Title" OFF**
2. **Expected Display**:
   ```
   ┌─────────────────────────────────┐
   │ AGGRESSIVE V 8s                 │  ← No title, no panda emoji
   │ You are doing 30% more damage   │
   └─────────────────────────────────┘
   ```

### **Test 4: Show Title, Hide Mood Icons**
1. **Toggle "🐼 Show Title" ON**
2. **Keep "😄 Show Mood Icons" OFF**
3. **Expected Display**:
   ```
   ┌─────────────────────────────────┐
   │ [🐼] Panda Boots Status         │  ← Panda emoji visible
   │ AGGRESSIVE V 8s                 │  ← Mood emoji hidden
   │ You are doing 30% more damage   │
   └─────────────────────────────────┘
   ```

---

## 🔍 **Debug Information**

### **Check Emoji System Status**
Look for these log messages:
- `"External PNG emoji files are available"` - Custom PNGs found
- `"No external PNG emoji files found"` - Using fallback system
- `"Generated PNG emoji renderer is available"` - Fallback textures working
- `"Unicode/Text Fallback"` - Using final fallback

### **Emoji Availability Check**
The mod logs which emojis are available:
```
External PNG Emoji Renderer Status:
Initialized: true
Registered textures: 4
  panda: Available
  aggressive: Available
  playful: Missing
  lazy: Available
```

---

## ✅ **Success Criteria**

### **External PNG System**:
- [ ] Custom PNG files are detected and used when present
- [ ] Fallback system works when PNG files are missing
- [ ] No crashes during emoji loading
- [ ] Proper scaling of custom emojis

### **Toggle Fix**:
- [ ] "Show Mood Icons" OFF hides only mood emojis (😡😴😄)
- [ ] "Show Mood Icons" OFF preserves panda emoji (🐼) in title
- [ ] "Show Title" OFF hides entire title including panda emoji
- [ ] "Show Title" ON + "Show Mood Icons" OFF shows panda but not mood emojis
- [ ] All combinations work correctly

### **Visual Quality**:
- [ ] Proper spacing between emojis and text
- [ ] No Unicode emojis in text when using PNG system
- [ ] Clean status display format: "AGGRESSIVE V 8s" (no extra symbols)
- [ ] Consistent emoji sizing and alignment

---

## 🚨 **Common Issues & Solutions**

### **PNG Files Not Loading**:
- Check file names are exact: `aggressive.png`, `playful.png`, `lazy.png`, `panda.png`
- Ensure files are in correct directory: `assets/panda-boots-status/textures/emoji/`
- Rebuild mod after adding PNG files
- Check file format is PNG with transparency

### **Toggle Not Working**:
- Ensure you're testing with an active mood effect
- Check that settings are being saved (close and reopen settings)
- Verify the correct toggle is being changed

### **Emoji Sizing Issues**:
- Custom PNGs should be 16x16 pixels for best results
- Larger images will be scaled down automatically
- Very small images may appear blurry when scaled up

---

## 🎉 **Expected Results**

**With Custom PNG Files**:
- Beautiful custom emojis that match your mod's theme
- Professional appearance with consistent styling
- No dependency on system emoji support

**Without Custom PNG Files**:
- Automatic fallback to generated textures or Unicode
- Mod continues to work normally
- No loss of functionality

**Toggle Behavior**:
- Intuitive control over emoji visibility
- Panda emoji independent of mood emoji settings
- Clean text-only display when emojis are disabled

The mod now provides maximum customization flexibility while maintaining robust fallback systems! 🐼✨
