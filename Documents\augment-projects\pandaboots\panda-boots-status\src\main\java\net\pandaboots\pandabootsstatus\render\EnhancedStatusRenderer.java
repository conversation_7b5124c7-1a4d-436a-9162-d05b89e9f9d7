package net.pandaboots.pandabootsstatus.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.config.ActionBarConfig;
import net.pandaboots.pandabootsstatus.status.MoodEffect;
import net.pandaboots.pandabootsstatus.status.MoodStatus;

/**
 * Enhanced status renderer with improved emoji support
 */
public class EnhancedStatusRenderer {
    private static final int PADDING = 8;
    private static final int LINE_HEIGHT = 12;
    private static final int EMOJI_SPACING = 2; // Space between emoji and text
    
    // Dragging state
    private static boolean isDragging = false;
    private static double dragStartX, dragStartY;
    private static int dragStartBoxX, dragStartBoxY;
    
    // Single effect display (latest wins)
    private static MoodEffect currentEffect = null;
    
    /**
     * Renders the status display on the HUD with enhanced emoji support
     * @param context The draw context
     */
    public static void renderStatus(DrawContext context) {
        ActionBarConfig config = ActionBarConfig.getInstance();
        if (!config.isShowStatusDisplay()) return;
        
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) return;
        
        // Use safe emoji renderer (handles initialization and fallbacks automatically)
        
        // Remove expired effect
        if (currentEffect != null && currentEffect.isExpired()) {
            currentEffect = null;
        }

        // Only render if we have an active effect
        if (currentEffect == null) return;
        
        renderStatusBox(context, config);
    }
    
    /**
     * Adds a new mood effect (replaces any existing effect - "latest wins" logic)
     * @param status The mood status
     * @param tier The tier level (1-5)
     * @param duration The duration in seconds (default 10)
     */
    public static void addMoodEffect(MoodStatus status, int tier, int duration) {
        // Create unique ID for this effect
        String id = status.name() + "_" + System.currentTimeMillis();
        
        // Replace any existing effect with new one (latest wins)
        currentEffect = new MoodEffect(id, status, tier, duration);
        
        PandaBootsStatus.LOGGER.info("Added mood effect: {} {}, duration: {} (replaced existing)", 
            status.getDisplayName(), tier, duration);
    }
    
    /**
     * Renders the status box with enhanced emoji support
     * @param context The draw context
     * @param config The configuration
     */
    private static void renderStatusBox(DrawContext context, ActionBarConfig config) {
        MinecraftClient client = MinecraftClient.getInstance();
        TextRenderer textRenderer = client.textRenderer;
        
        int screenWidth = client.getWindow().getScaledWidth();
        float scale = config.getStatusScale();
        
        // Calculate content dimensions
        String title = "Panda Boots Status"; // Text only, emoji rendered separately
        int titleWidth = config.isShowTitle() ? (textRenderer.getWidth(title) + SafeEmojiRenderer.getEmojiWidth(scale) + EMOJI_SPACING) : 0;
        int maxContentWidth = titleWidth;
        
        // Calculate lines needed for single effect
        int linesPerEffect = 1; // Status line
        if (config.isShowEffectDescriptions()) linesPerEffect++;
        
        // Find the widest content line for the single effect
        String effectDisplayText = getStatusTextWithoutEmoji(currentEffect, false); // Always text-only for width calculation
        int statusWidth = textRenderer.getWidth(effectDisplayText);
        if (config.isShowMoodIcons()) {
            statusWidth += SafeEmojiRenderer.getEmojiWidth(scale) + EMOJI_SPACING;
        }
        maxContentWidth = Math.max(maxContentWidth, statusWidth);
        
        if (config.isShowEffectDescriptions()) {
            String descText = currentEffect.getEffectDescription();
            int descWidth = textRenderer.getWidth(descText);
            maxContentWidth = Math.max(maxContentWidth, descWidth);
        }
        
        // Calculate total lines (title is optional now)
        int titleLines = config.isShowTitle() ? 1 : 0;
        int totalLines = titleLines + linesPerEffect;
        
        // Calculate box dimensions
        int boxWidth = (int) ((maxContentWidth + PADDING * 2) * scale);
        int boxHeight = (int) ((totalLines * LINE_HEIGHT + PADDING * 2) * scale);
        
        // Calculate box position
        int boxX = config.getStatusX();
        int boxY = config.getStatusY();
        
        // Center horizontally if x is -1
        if (boxX == -1) {
            boxX = (screenWidth / 2) - (boxWidth / 2);
        }
        
        // Prepare colors
        int shadowColor = 0x40000000; // Semi-transparent black shadow
        int backgroundColor = (config.getBackgroundOpacity() * 255 / 100 << 24) | config.getBackgroundColorInt();
        int borderColor = 0xFF000000 | config.getBorderColorInt();

        // Draw background and border only if enabled
        if (config.isShowBackgroundAndBorder()) {
            // Draw shadow for depth if enabled
            if (config.getShadowIntensity() > 0) {
                int shadowOffset = Math.max(1, config.getShadowIntensity() / 25);
                context.fill(boxX + shadowOffset, boxY + shadowOffset,
                            boxX + boxWidth + shadowOffset, boxY + boxHeight + shadowOffset, shadowColor);
            }

            // Draw background
            context.fill(boxX, boxY, boxX + boxWidth, boxY + boxHeight, backgroundColor);

            // Draw border if enabled
            if (config.getBorderThickness() > 0) {
                for (int i = 0; i < config.getBorderThickness(); i++) {
                    context.drawBorder(boxX - i, boxY - i, boxWidth + (i * 2), boxHeight + (i * 2), borderColor);
                }
            }
        }
        
        // Draw content with scaling
        context.getMatrices().push();
        context.getMatrices().scale(scale, scale, 1.0f);
        
        int scaledX = (int) (boxX / scale) + PADDING;
        int scaledY = (int) (boxY / scale) + PADDING;
        
        // Draw title (if enabled) with panda emoji
        if (config.isShowTitle()) {
            // Draw panda emoji
            SafeEmojiRenderer.renderPandaEmoji(context, scaledX, scaledY, 1.0f);

            // Draw title text next to emoji
            int titleTextX = scaledX + SafeEmojiRenderer.getEmojiWidth(1.0f) + EMOJI_SPACING;
            context.drawText(textRenderer, title, titleTextX, scaledY, config.getTextColorInt(), false);
            scaledY += LINE_HEIGHT;
        }

        // Draw single active effect with enhanced emoji
        // Draw mood emoji (if enabled) and status text
        int currentX = scaledX;
        
        if (config.isShowMoodIcons()) {
            // Draw mood emoji
            SafeEmojiRenderer.renderMoodEmoji(context, currentEffect.getStatus(), currentX, scaledY, 1.0f);
            currentX += SafeEmojiRenderer.getEmojiWidth(1.0f) + EMOJI_SPACING;
        }
        
        // Draw status text
        String statusText = getStatusTextWithoutEmoji(currentEffect, false); // Always without emoji since we render it separately
        int statusColor = getEffectColor(currentEffect, config);
        context.drawText(textRenderer, statusText, currentX, scaledY, statusColor, false);
        scaledY += LINE_HEIGHT;

        // Draw effect description if enabled
        if (config.isShowEffectDescriptions()) {
            String descText = currentEffect.getEffectDescription();
            int descColor = 0xFF888888; // Muted gray
            context.drawText(textRenderer, descText, scaledX, scaledY, descColor, false);
            scaledY += LINE_HEIGHT;
        }
        
        context.getMatrices().pop();
    }
    
    /**
     * Gets status text without emoji (for width calculation and rendering)
     * Since we render emojis separately now, this always returns text-only
     */
    private static String getStatusTextWithoutEmoji(MoodEffect effect, boolean includeEmoji) {
        // Always return text without emojis since we render them separately
        // Format: "AGGRESSIVE V 8s" (no emoji)
        MoodStatus status = effect.getStatus();
        String tierRoman = convertToRoman(effect.getTier());
        return status.getDisplayName() + " " + tierRoman + " " + effect.getRemainingSeconds() + "s";
    }

    /**
     * Converts a tier number to Roman numerals
     * @param tier The tier number (1-5)
     * @return Roman numeral string
     */
    private static String convertToRoman(int tier) {
        switch (tier) {
            case 1: return "I";
            case 2: return "II";
            case 3: return "III";
            case 4: return "IV";
            case 5: return "V";
            default: return String.valueOf(tier);
        }
    }
    
    /**
     * Gets the color for an effect based on configuration
     * @param effect The mood effect
     * @param config The configuration
     * @return Color integer
     */
    private static int getEffectColor(MoodEffect effect, ActionBarConfig config) {
        switch (effect.getStatus()) {
            case LAZY:
                return config.getLazyColorInt();
            case AGGRESSIVE:
                return config.getAggressiveColorInt();
            case PLAYFUL:
                return config.getPlayfulColorInt();
            default:
                return config.getTextColorInt();
        }
    }
    
    // Delegate methods to maintain compatibility with existing StatusRenderer
    public static boolean handleMouseClick(double mouseX, double mouseY) {
        return StatusRenderer.handleMouseClick(mouseX, mouseY);
    }
    
    public static void handleMouseDrag(double mouseX, double mouseY) {
        StatusRenderer.handleMouseDrag(mouseX, mouseY);
    }
    
    public static void handleMouseRelease() {
        StatusRenderer.handleMouseRelease();
    }
    
    public static int[] getBoxBounds() {
        return StatusRenderer.getBoxBounds();
    }
    
    public static boolean isDragging() {
        return StatusRenderer.isDragging();
    }
    
    public static void clearAllEffects() {
        currentEffect = null;
        StatusRenderer.clearAllEffects();
    }
}
