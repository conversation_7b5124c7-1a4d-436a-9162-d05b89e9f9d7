package net.pandaboots.pandabootsstatus.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.texture.NativeImageBackedTexture;
import net.minecraft.util.Identifier;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * File-based emoji loader that reads PNG files from .minecraft/config/panda-boots-status/
 */
public class FileBasedEmojiLoader {
    private static final Map<String, Identifier> customTextures = new HashMap<>();
    private static final int EMOJI_SIZE = 8; // Render size (like default emojis)
    private static boolean initialized = false;
    
    // Config directory path
    private static Path configDir;
    
    /**
     * Initializes the file-based emoji loader
     */
    public static void initialize() {
        if (initialized) return;
        
        try {
            // Get Minecraft directory and create config path
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null) {
                PandaBootsStatus.LOGGER.warn("Client not available for emoji initialization");
                return;
            }
            
            configDir = Paths.get(client.runDirectory.getAbsolutePath(), "config", "panda-boots-status");
            
            // Create config directory if it doesn't exist
            if (!Files.exists(configDir)) {
                Files.createDirectories(configDir);
                PandaBootsStatus.LOGGER.info("Created emoji config directory: {}", configDir);
                
                // Generate default placeholder emoji files
                generateDefaultEmojiFiles();
            }
            
            // Load emoji files
            loadEmojiFile("panda");
            loadEmojiFile("aggressive");
            loadEmojiFile("playful");
            loadEmojiFile("lazy");
            
            initialized = true;
            PandaBootsStatus.LOGGER.info("File-based emoji loader initialized with {} emojis from: {}", 
                customTextures.size(), configDir);
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to initialize file-based emoji loader", e);
            initialized = true;
        }
    }
    
    /**
     * Generates default placeholder emoji files
     */
    private static void generateDefaultEmojiFiles() {
        try {
            generateDefaultEmoji("panda", Color.BLACK, Color.WHITE, "P");
            generateDefaultEmoji("aggressive", Color.RED, Color.WHITE, "!");
            generateDefaultEmoji("playful", Color.GREEN, Color.WHITE, "^");
            generateDefaultEmoji("lazy", Color.BLUE, Color.WHITE, "z");
            
            PandaBootsStatus.LOGGER.info("Generated default placeholder emoji files");
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to generate default emoji files", e);
        }
    }
    
    /**
     * Generates a default placeholder emoji file
     */
    private static void generateDefaultEmoji(String name, Color bgColor, Color textColor, String text) {
        try {
            // Create a 16x16 image (same as default emojis)
            BufferedImage image = new BufferedImage(16, 16, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = image.createGraphics();

            // Enable antialiasing for better text rendering
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            // Fill background
            g2d.setColor(bgColor);
            g2d.fillRect(0, 0, 16, 16);

            // Draw border
            g2d.setColor(Color.GRAY);
            g2d.drawRect(0, 0, 15, 15);

            // Draw text
            g2d.setColor(textColor);
            g2d.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 12));
            FontMetrics fm = g2d.getFontMetrics();
            int textX = (16 - fm.stringWidth(text)) / 2;
            int textY = (16 - fm.getHeight()) / 2 + fm.getAscent();
            g2d.drawString(text, textX, textY);

            g2d.dispose();

            // Save to file
            Path filePath = configDir.resolve(name + ".png");
            ImageIO.write(image, "PNG", filePath.toFile());

            PandaBootsStatus.LOGGER.debug("Generated default emoji: {}", filePath);

        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to generate default emoji for {}: {}", name, e.getMessage());
        }
    }
    
    /**
     * Loads an emoji file from the config directory
     */
    private static void loadEmojiFile(String emojiName) {
        try {
            Path filePath = configDir.resolve(emojiName + ".png");
            
            if (!Files.exists(filePath)) {
                PandaBootsStatus.LOGGER.debug("Emoji file not found: {}", filePath);
                return;
            }
            
            // Load the image directly (like default emojis - NO RESIZING)
            try (InputStream inputStream = Files.newInputStream(filePath)) {
                NativeImage nativeImage = NativeImage.read(inputStream);

                // Register texture directly (like default emojis)
                registerTexture(emojiName, nativeImage);

            } catch (Exception imageError) {
                PandaBootsStatus.LOGGER.warn("Failed to load image for {}: {}", emojiName, imageError.getMessage());
                return;
            }
            
            PandaBootsStatus.LOGGER.info("Loaded custom emoji: {} from {}", emojiName, filePath);
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to load emoji file {}: {}", emojiName, e.getMessage());
        }
    }

    
    /**
     * Registers a texture with Minecraft
     */
    private static void registerTexture(String emojiName, NativeImage image) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null) {
                PandaBootsStatus.LOGGER.warn("Client not available for texture registration: {}", emojiName);
                return;
            }
            
            NativeImageBackedTexture texture = new NativeImageBackedTexture(image);
            Identifier textureId = Identifier.of("panda-boots-status", "file_emoji_" + emojiName);
            
            client.getTextureManager().registerTexture(textureId, texture);
            customTextures.put(emojiName, textureId);
            
            PandaBootsStatus.LOGGER.debug("Registered file-based emoji texture: {} -> {}", emojiName, textureId);
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to register texture for {}: {}", emojiName, e.getMessage());
        }
    }
    
    /**
     * Checks if a custom emoji is available
     */
    public static boolean hasCustomEmoji(String emojiName) {
        return customTextures.containsKey(emojiName);
    }
    
    /**
     * Gets the texture identifier for a custom emoji
     */
    public static Identifier getCustomEmojiTexture(String emojiName) {
        return customTextures.get(emojiName);
    }
    
    /**
     * Reloads all emoji files (for when files are changed)
     */
    public static void reload() {
        PandaBootsStatus.LOGGER.info("Reloading file-based emojis...");

        // Clear existing textures and unregister them
        for (Map.Entry<String, Identifier> entry : customTextures.entrySet()) {
            try {
                MinecraftClient client = MinecraftClient.getInstance();
                if (client != null && client.getTextureManager() != null) {
                    // Note: Minecraft doesn't provide a direct way to unregister textures,
                    // but they will be replaced when we register new ones with the same ID
                    PandaBootsStatus.LOGGER.debug("Preparing to replace texture: {}", entry.getValue());
                }
            } catch (Exception e) {
                PandaBootsStatus.LOGGER.warn("Error preparing texture replacement for {}: {}", entry.getKey(), e.getMessage());
            }
        }

        customTextures.clear();
        initialized = false;
        initialize();

        PandaBootsStatus.LOGGER.info("File-based emoji reload complete. Loaded {} emojis.", customTextures.size());
    }
    
    /**
     * Gets the config directory path
     */
    public static String getConfigDirectory() {
        return configDir != null ? configDir.toString() : "Not initialized";
    }
    
    /**
     * Gets debug information
     */
    public static String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("File-Based Emoji Loader Status:\n");
        info.append("Initialized: ").append(initialized).append("\n");
        info.append("Config Directory: ").append(getConfigDirectory()).append("\n");
        info.append("Loaded emojis: ").append(customTextures.size()).append("\n");
        
        for (Map.Entry<String, Identifier> entry : customTextures.entrySet()) {
            info.append("  ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }
        
        return info.toString();
    }
}
