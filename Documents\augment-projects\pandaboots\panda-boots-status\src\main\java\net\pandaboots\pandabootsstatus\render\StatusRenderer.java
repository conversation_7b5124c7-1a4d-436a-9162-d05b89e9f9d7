package net.pandaboots.pandabootsstatus.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.config.ActionBarConfig;
import net.pandaboots.pandabootsstatus.status.MoodEffect;
import net.pandaboots.pandabootsstatus.status.MoodStatus;

/**
 * Handles rendering of the status display on the HUD
 */
public class StatusRenderer {
    private static final int PADDING = 8;
    private static final int LINE_HEIGHT = 12;
    
    // Dragging state
    private static boolean isDragging = false;
    private static double dragStartX, dragStartY;
    private static int dragStartBoxX, dragStartBoxY;
    
    // Single effect display (latest wins)
    private static MoodEffect currentEffect = null;
    
    /**
     * Renders the status display on the HUD
     * @param context The draw context
     */
    public static void renderStatus(DrawContext context) {
        ActionBarConfig config = ActionBarConfig.getInstance();
        if (!config.isShowStatusDisplay()) return;
        
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) return;
        
        // Remove expired effect
        if (currentEffect != null && currentEffect.isExpired()) {
            currentEffect = null;
        }

        // Only render if we have an active effect
        if (currentEffect == null) return;
        
        renderStatusBox(context, config);
    }
    
    /**
     * Adds a new mood effect (replaces any existing effect - "latest wins" logic)
     * @param status The mood status
     * @param tier The tier level (1-5)
     * @param duration The duration in seconds (default 10)
     */
    public static void addMoodEffect(MoodStatus status, int tier, int duration) {
        // Create unique ID for this effect
        String id = status.name() + "_" + System.currentTimeMillis();
        
        // Replace any existing effect with new one (latest wins)
        currentEffect = new MoodEffect(id, status, tier, duration);
        
        PandaBootsStatus.LOGGER.info("Added mood effect: {} {}, duration: {} (replaced existing)", 
            status.getDisplayName(), tier, duration);
    }
    
    /**
     * Renders the status box with modern styling
     * @param context The draw context
     * @param config The configuration
     */
    private static void renderStatusBox(DrawContext context, ActionBarConfig config) {
        MinecraftClient client = MinecraftClient.getInstance();
        TextRenderer textRenderer = client.textRenderer;
        
        int screenWidth = client.getWindow().getScaledWidth();
        float scale = config.getStatusScale();
        
        // Calculate content dimensions
        String title = "🐼 Panda Boots Status";
        int titleWidth = config.isShowTitle() ? textRenderer.getWidth(title) : 0;
        int maxContentWidth = titleWidth;
        
        // Calculate lines needed for single effect
        int linesPerEffect = 1; // Status line
        if (config.isShowEffectDescriptions()) linesPerEffect++;
        
        // Find the widest content line for the single effect
        String effectDisplayText = currentEffect.getDisplayText(config.isShowMoodIcons());
        int statusWidth = textRenderer.getWidth(effectDisplayText);
        maxContentWidth = Math.max(maxContentWidth, statusWidth);
        
        if (config.isShowEffectDescriptions()) {
            String descText = currentEffect.getEffectDescription();
            int descWidth = textRenderer.getWidth(descText);
            maxContentWidth = Math.max(maxContentWidth, descWidth);
        }
        
        // Calculate total lines (title is optional now)
        int titleLines = config.isShowTitle() ? 1 : 0;
        int totalLines = titleLines + linesPerEffect;
        
        // Calculate box dimensions
        int boxWidth = (int) ((maxContentWidth + PADDING * 2) * scale);
        int boxHeight = (int) ((totalLines * LINE_HEIGHT + PADDING * 2) * scale);
        
        // Calculate box position
        int boxX = config.getStatusX();
        int boxY = config.getStatusY();
        
        // Center horizontally if x is -1
        if (boxX == -1) {
            boxX = (screenWidth / 2) - (boxWidth / 2);
        }
        
        // Prepare colors
        int shadowColor = 0x40000000; // Semi-transparent black shadow
        int backgroundColor = (config.getBackgroundOpacity() * 255 / 100 << 24) | config.getBackgroundColorInt();
        int borderColor = 0xFF000000 | config.getBorderColorInt();

        // Draw background and border only if enabled
        if (config.isShowBackgroundAndBorder()) {
            // Draw shadow for depth if enabled
            if (config.getShadowIntensity() > 0) {
                int shadowOffset = Math.max(1, config.getShadowIntensity() / 25);
                context.fill(boxX + shadowOffset, boxY + shadowOffset,
                            boxX + boxWidth + shadowOffset, boxY + boxHeight + shadowOffset, shadowColor);
            }

            // Draw background
            context.fill(boxX, boxY, boxX + boxWidth, boxY + boxHeight, backgroundColor);

            // Draw border if enabled
            if (config.getBorderThickness() > 0) {
                for (int i = 0; i < config.getBorderThickness(); i++) {
                    context.drawBorder(boxX - i, boxY - i, boxWidth + (i * 2), boxHeight + (i * 2), borderColor);
                }
            }
        }
        
        // Draw content with scaling
        context.getMatrices().push();
        context.getMatrices().scale(scale, scale, 1.0f);
        
        int scaledX = (int) (boxX / scale) + PADDING;
        int scaledY = (int) (boxY / scale) + PADDING;
        
        // Draw title (if enabled)
        if (config.isShowTitle()) {
            context.drawText(textRenderer, title, scaledX, scaledY, config.getTextColorInt(), false);
            scaledY += LINE_HEIGHT;
        }

        // Draw single active effect
        // Draw status text
        String statusText = currentEffect.getDisplayText(config.isShowMoodIcons());
        int statusColor = getEffectColor(currentEffect, config);
        context.drawText(textRenderer, statusText, scaledX, scaledY, statusColor, false);
        scaledY += LINE_HEIGHT;

        // Progress bar removed as per requirements

        // Draw effect description if enabled
        if (config.isShowEffectDescriptions()) {
            String descText = currentEffect.getEffectDescription();
            int descColor = 0xFF888888; // Muted gray
            context.drawText(textRenderer, descText, scaledX, scaledY, descColor, false);
            scaledY += LINE_HEIGHT;
        }
        
        context.getMatrices().pop();
    }

    /**
     * Handles mouse click for dragging
     * @param mouseX Mouse X position
     * @param mouseY Mouse Y position
     * @return true if the click was handled
     */
    public static boolean handleMouseClick(double mouseX, double mouseY) {
        ActionBarConfig config = ActionBarConfig.getInstance();
        if (!config.isDraggable() || currentEffect == null) return false;
        
        int[] bounds = getBoxBounds();
        if (bounds == null) return false;
        
        int boxX = bounds[0];
        int boxY = bounds[1];
        int boxWidth = bounds[2];
        int boxHeight = bounds[3];
        
        // Check if click is within the box
        if (mouseX >= boxX && mouseX <= boxX + boxWidth && 
            mouseY >= boxY && mouseY <= boxY + boxHeight) {
            
            isDragging = true;
            dragStartX = mouseX;
            dragStartY = mouseY;
            dragStartBoxX = config.getStatusX();
            dragStartBoxY = config.getStatusY();
            
            return true;
        }
        
        return false;
    }

    /**
     * Handles mouse drag
     * @param mouseX Mouse X position
     * @param mouseY Mouse Y position
     */
    public static void handleMouseDrag(double mouseX, double mouseY) {
        if (!isDragging) return;
        
        ActionBarConfig config = ActionBarConfig.getInstance();
        
        double deltaX = mouseX - dragStartX;
        double deltaY = mouseY - dragStartY;
        
        int newX = dragStartBoxX + (int) deltaX;
        int newY = dragStartBoxY + (int) deltaY;
        
        config.setStatusX(newX);
        config.setStatusY(newY);
    }

    /**
     * Handles mouse release
     */
    public static void handleMouseRelease() {
        isDragging = false;
    }

    /**
     * Gets the current box bounds for dragging
     * @return int array [x, y, width, height] or null if no effects
     */
    public static int[] getBoxBounds() {
        if (currentEffect == null) return null;
        
        ActionBarConfig config = ActionBarConfig.getInstance();
        MinecraftClient client = MinecraftClient.getInstance();
        TextRenderer textRenderer = client.textRenderer;
        
        int screenWidth = client.getWindow().getScaledWidth();
        float scale = config.getStatusScale();
        
        // Calculate box dimensions (same as renderStatusBox)
        int maxContentWidth = 0;
        if (config.isShowTitle()) {
            String title = "🐼 Panda Boots Status";
            int titleWidth = textRenderer.getWidth(title);
            maxContentWidth = titleWidth;
        }
        
        // Calculate for single effect
        String text = currentEffect.getDisplayText(config.isShowMoodIcons());
        int textWidth = textRenderer.getWidth(text);
        maxContentWidth = Math.max(maxContentWidth, textWidth);

        if (config.isShowEffectDescriptions()) {
            String descText = currentEffect.getEffectDescription();
            int descWidth = textRenderer.getWidth(descText);
            maxContentWidth = Math.max(maxContentWidth, descWidth);
        }
        
        int boxWidth = (int) ((maxContentWidth + PADDING * 2) * scale);
        // Calculate height for single effect
        int linesForEffect = 1; // Status line
        if (config.isShowEffectDescriptions()) linesForEffect++;

        // Calculate total lines (title is optional now)
        int titleLines = config.isShowTitle() ? 1 : 0;
        int boxHeight = (int) ((LINE_HEIGHT * (titleLines + linesForEffect) + PADDING * 2) * scale);
        
        int boxX = config.getStatusX();
        int boxY = config.getStatusY();
        
        if (boxX == -1) {
            boxX = (screenWidth / 2) - (boxWidth / 2);
        }
        
        return new int[]{boxX, boxY, boxWidth, boxHeight};
    }

    /**
     * Checks if currently dragging
     * @return true if dragging, false otherwise
     */
    public static boolean isDragging() {
        return isDragging;
    }

    /**
     * Gets the color for an effect based on configuration
     * @param effect The mood effect
     * @param config The configuration
     * @return Color integer
     */
    private static int getEffectColor(MoodEffect effect, ActionBarConfig config) {
        switch (effect.getStatus()) {
            case LAZY:
                return config.getLazyColorInt();
            case AGGRESSIVE:
                return config.getAggressiveColorInt();
            case PLAYFUL:
                return config.getPlayfulColorInt();
            default:
                return config.getTextColorInt();
        }
    }

    /**
     * Clears the current active effect
     */
    public static void clearAllEffects() {
        currentEffect = null;
    }
}
