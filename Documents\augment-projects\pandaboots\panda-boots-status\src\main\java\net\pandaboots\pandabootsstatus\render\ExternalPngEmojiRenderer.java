package net.pandaboots.pandabootsstatus.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.texture.MissingSprite;
import net.minecraft.util.Identifier;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.status.MoodStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * External PNG emoji renderer that loads actual PNG files from the assets directory
 */
public class ExternalPngEmojiRenderer {
    private static final Map<String, Identifier> emojiTextures = new HashMap<>();
    private static boolean initialized = false;
    
    // Emoji size in pixels (will be scaled as needed) - half size for subtle emojis
    private static final int EMOJI_SIZE = 8;   // Half size from original 16 to 8
    
    /**
     * Initializes the external PNG emoji rendering system
     */
    public static void initialize() {
        if (initialized) return;
        
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null) {
                PandaBootsStatus.LOGGER.warn("Minecraft client not available, deferring emoji initialization");
                return;
            }
            
            // Register emoji texture identifiers (Minecraft will load the PNG files automatically)
            registerEmojiTexture("panda", "panda");
            registerEmojiTexture("aggressive", "aggressive");
            registerEmojiTexture("playful", "playful");
            registerEmojiTexture("lazy", "lazy");
            
            initialized = true;
            PandaBootsStatus.LOGGER.info("External PNG emoji renderer initialized with {} textures", emojiTextures.size());
            
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to initialize external PNG emoji renderer", e);
            initialized = true; // Set to true to prevent repeated attempts
        }
    }
    
    /**
     * Registers an emoji texture identifier
     */
    private static void registerEmojiTexture(String emojiName, String fileName) {
        try {
            // Minecraft texture identifiers don't include the .png extension
            Identifier textureId = Identifier.of("panda-boots-status", "textures/emoji/" + fileName);
            emojiTextures.put(emojiName, textureId);
            PandaBootsStatus.LOGGER.info("Registered emoji texture: {} -> {}", emojiName, textureId);

        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to register emoji texture for: " + emojiName, e);
        }
    }
    
    /**
     * Checks if a PNG file exists for the given emoji
     */
    public static boolean hasEmojiTexture(String emojiName) {
        if (!initialized) {
            initialize();
        }

        Identifier textureId = emojiTextures.get(emojiName);
        if (textureId == null) {
            PandaBootsStatus.LOGGER.debug("No texture ID registered for emoji: {}", emojiName);
            return false;
        }

        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null || client.getTextureManager() == null) {
                PandaBootsStatus.LOGGER.debug("Client or texture manager not available for emoji: {}", emojiName);
                return false;
            }

            // Try to load the texture - this will attempt to load the PNG file
            var texture = client.getTextureManager().getTexture(textureId);
            boolean exists = texture != null && !textureId.equals(MissingSprite.getMissingSpriteId());

            PandaBootsStatus.LOGGER.debug("Texture check for {}: {} (texture: {})", emojiName, exists ? "Available" : "Missing", textureId);
            return exists;

        } catch (Exception e) {
            PandaBootsStatus.LOGGER.warn("Texture check failed for {}: {}", emojiName, e.getMessage());
            return false;
        }
    }
    
    /**
     * Renders an emoji from external PNG file
     */
    public static boolean renderEmoji(DrawContext context, String emojiName, int x, int y, float scale) {
        if (!initialized) {
            initialize();
        }

        Identifier textureId = emojiTextures.get(emojiName);
        if (textureId == null) {
            PandaBootsStatus.LOGGER.debug("No texture ID for emoji: {}", emojiName);
            return false;
        }

        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null || client.getTextureManager() == null) {
                PandaBootsStatus.LOGGER.debug("Client/texture manager not available for emoji: {}", emojiName);
                return false;
            }

            // Simply try to render - Minecraft will load the texture automatically if it exists
            // No need to manually bind the texture

            int size = (int) (EMOJI_SIZE * scale);

            // Render the PNG texture
            context.drawTexture(textureId, x, y, 0, 0, size, size, size, size);

            PandaBootsStatus.LOGGER.debug("Successfully rendered external PNG emoji: {} at ({}, {}) size {}", emojiName, x, y, size);
            return true;

        } catch (Exception e) {
            PandaBootsStatus.LOGGER.warn("Failed to render emoji {}: {}", emojiName, e.getMessage());
            return false;
        }
    }
    
    /**
     * Gets the emoji name for a mood status
     */
    public static String getEmojiName(MoodStatus status) {
        switch (status) {
            case AGGRESSIVE: return "aggressive";
            case LAZY: return "lazy";
            case PLAYFUL: return "playful";
            default: return null;
        }
    }
    
    /**
     * Renders a mood emoji from external PNG
     */
    public static boolean renderMoodEmoji(DrawContext context, MoodStatus status, int x, int y, float scale) {
        String emojiName = getEmojiName(status);
        if (emojiName != null) {
            return renderEmoji(context, emojiName, x, y, scale);
        }
        return false;
    }
    
    /**
     * Renders the panda emoji from external PNG
     */
    public static boolean renderPandaEmoji(DrawContext context, int x, int y, float scale) {
        return renderEmoji(context, "panda", x, y, scale);
    }
    
    /**
     * Gets the width of an emoji when rendered
     */
    public static int getEmojiWidth(float scale) {
        return (int) (EMOJI_SIZE * scale);
    }
    
    /**
     * Gets the height of an emoji when rendered
     */
    public static int getEmojiHeight(float scale) {
        return (int) (EMOJI_SIZE * scale);
    }
    
    /**
     * Lists all available emoji textures
     */
    public static Map<String, Boolean> getEmojiAvailability() {
        Map<String, Boolean> availability = new HashMap<>();
        for (String emojiName : emojiTextures.keySet()) {
            availability.put(emojiName, hasEmojiTexture(emojiName));
        }
        return availability;
    }
    
    /**
     * Gets debug information about the emoji system
     */
    public static String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("External PNG Emoji Renderer Status:\n");
        info.append("Initialized: ").append(initialized).append("\n");
        info.append("Registered textures: ").append(emojiTextures.size()).append("\n");
        
        for (Map.Entry<String, Boolean> entry : getEmojiAvailability().entrySet()) {
            info.append("  ").append(entry.getKey()).append(": ")
                .append(entry.getValue() ? "Available" : "Missing").append("\n");
        }
        
        return info.toString();
    }
}
