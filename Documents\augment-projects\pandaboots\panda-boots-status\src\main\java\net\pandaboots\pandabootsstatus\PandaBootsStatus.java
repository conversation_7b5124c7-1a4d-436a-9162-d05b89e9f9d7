package net.pandaboots.pandabootsstatus;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientLifecycleEvents;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.pandaboots.pandabootsstatus.config.ActionBarConfig;
import net.pandaboots.pandabootsstatus.gui.ActionBarSettingsScreen;
import net.pandaboots.pandabootsstatus.logger.MessageLogger;
import net.pandaboots.pandabootsstatus.render.StatusRenderer;
import net.pandaboots.pandabootsstatus.render.EnhancedStatusRenderer;
import net.pandaboots.pandabootsstatus.render.SafeEmojiRenderer;
import net.pandaboots.pandabootsstatus.render.ExternalPngEmojiRenderer;
import org.lwjgl.glfw.GLFW;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PandaBootsStatus implements ClientModInitializer {
    public static final String MOD_ID = "panda-boots-status";
    public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

    // Keybinding for opening settings
    private static KeyBinding settingsKeyBinding;

    @Override
    public void onInitializeClient() {
        LOGGER.info("Initializing Panda Boots Status v1.0.0...");

        // Initialize configuration
        ActionBarConfig.getInstance();

        // Initialize message logger
        MessageLogger.initialize();

        // Initialize safe emoji renderer (handles PNG and fallbacks automatically)
        LOGGER.info("Initializing safe emoji renderer system...");

        // Register keybinding for settings
        settingsKeyBinding = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.panda-boots-status.settings",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_O,
            "category.panda-boots-status"
        ));

        // Register HUD renderer for status display with enhanced emoji support
        HudRenderCallback.EVENT.register((drawContext, tickDelta) -> {
            EnhancedStatusRenderer.renderStatus(drawContext);
        });

        // Register tick events for mouse handling and keybinding
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            // Check for settings key press
            if (settingsKeyBinding.wasPressed()) {
                client.setScreen(new ActionBarSettingsScreen(client.currentScreen));
            }

            // Handle mouse input for dragging (only when no screen is open)
            if (client.currentScreen == null) {
                handleMouseInput(client);
            }
        });

        // Register shutdown hook to ensure proper cleanup
        ClientLifecycleEvents.CLIENT_STOPPING.register(client -> {
            LOGGER.info("Shutting down Panda Boots Status...");
            MessageLogger.shutdown();
            EnhancedStatusRenderer.clearAllEffects();
        });

        LOGGER.info("Panda Boots Status v1.0.0 initialized successfully!");
    }

    /**
     * Handles mouse input for dragging the status box
     * @param client The Minecraft client
     */
    private static void handleMouseInput(MinecraftClient client) {
        if (client.mouse == null) return;

        double mouseX = client.mouse.getX() * client.getWindow().getScaledWidth() / client.getWindow().getWidth();
        double mouseY = client.mouse.getY() * client.getWindow().getScaledHeight() / client.getWindow().getHeight();

        // Check for mouse button state
        boolean leftPressed = GLFW.glfwGetMouseButton(client.getWindow().getHandle(), GLFW.GLFW_MOUSE_BUTTON_LEFT) == GLFW.GLFW_PRESS;

        if (leftPressed) {
            if (!EnhancedStatusRenderer.isDragging()) {
                // Try to start dragging
                EnhancedStatusRenderer.handleMouseClick(mouseX, mouseY);
            } else {
                // Continue dragging
                EnhancedStatusRenderer.handleMouseDrag(mouseX, mouseY);
            }
        } else {
            // Mouse released
            if (EnhancedStatusRenderer.isDragging()) {
                EnhancedStatusRenderer.handleMouseRelease();
            }
        }
    }

    /**
     * Called by the mixin when an action bar message is received
     * @param message The action bar message text
     */
    public static void onActionBarMessage(String message) {
        if (message != null && !message.trim().isEmpty()) {
            MessageLogger.logMessage(message);
        }
    }
}
