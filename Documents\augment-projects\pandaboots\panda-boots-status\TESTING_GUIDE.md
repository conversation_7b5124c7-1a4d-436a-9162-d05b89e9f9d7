# 🧪 Panda Boots Status - Complete Testing Guide

## 🎯 **Testing All Enhanced Features**

### **1. Settings Screen Modifications Testing**

#### **Test Removed Toggles** ✅
1. Press **'O' key** to open settings
2. **Verify MISSING toggles**:
   - ❌ "Enable Dragging" toggle (should NOT be present)
   - ❌ "Show Progress Bar" toggle (should NOT be present)
   - ❌ "Show Timer" toggle (should NOT be present)

#### **Test New Controls** ✅
1. **"🐼 Show Title" toggle**:
   - Toggle OFF → Title "🐼 Panda Boots Status" should disappear
   - Toggle ON → Title should reappear
   - **Panda emoji should ALWAYS be visible when title is shown**

2. **"🎨 Toggle Background & Border" button**:
   - Click button → Both background and border should disappear simultaneously
   - Click again → Both should reappear together
   - **Should affect BOTH elements at once**

### **2. Position/Location System Testing**

#### **Test Readwork Integration** ✅
1. Press **'O' key** → Settings screen
2. Click **"📍 Change Position & Scale"** → Position screen opens
3. **Verify features**:
   - Live preview status box visible
   - Grid lines and center markers
   - Real-time coordinate display
   - Scale percentage display

#### **Test Position Controls** ✅
1. **Dragging**:
   - Click and drag preview box → Position updates in real-time
   - Release → New position saved
   - Crosshair cursor appears during drag

2. **Button Controls**:
   - **← Left / Right →** → Fine position adjustment
   - **↑ Up / Down ↓** → Vertical adjustment
   - **Preset buttons** → Top Center, Center, Bottom, Reset

3. **Scale Controls**:
   - **Scale -** → Decreases size
   - **Scale +** → Increases size
   - **Scale 100%** → Resets to normal size
   - **Scale 80%** → Sets to 80%

### **3. Status Display Format Testing**

#### **Test Format Changes** ✅
```bash
# Test command
/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!"}

# Expected result: "😡 AGGRESSIVE V 8s" (NO DASH)
# Old format: "😡 AGGRESSIVE V - 8s" (with dash)
```

#### **Test Mood Icons Toggle** ✅
1. **With "Show Mood Icons" ON**:
   - Status: "😡 AGGRESSIVE V 8s" (with mood emoji)
   - Title: "🐼 Panda Boots Status" (panda emoji preserved)

2. **With "Show Mood Icons" OFF**:
   - Status: "AGGRESSIVE V 8s" (NO mood emoji)
   - Title: "🐼 Panda Boots Status" (panda emoji STILL visible)

### **4. Emoji Implementation Testing**

#### **Test Apple Emoji Support** ✅
```bash
# Test all mood types
/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!"}
# Expected: 😡 (angry face - updated from generic angry)

/title @s actionbar {"text":"Mood Swings V has made you feel lazy!"}
# Expected: 😴 (sleeping face)

/title @s actionbar {"text":"Mood Swings V has made you feel playful!"}
# Expected: 😄 (grinning face)
```

#### **Verify Emoji Rendering** ✅
- All emojis should render as proper Apple emoji graphics
- No text symbols or placeholder characters
- Consistent appearance across different systems

### **5. Complete Rebranding Testing**

#### **Test Mod Identity** ✅
1. **In-game verification**:
   - Mod list shows "Panda Boots Status" (not Action Bar Logger)
   - Settings screen title: "Panda Boots Status Settings"
   - Keybinding category: "Panda Boots Status"

2. **File verification**:
   - JAR file: `panda-boots-status-1.0.0.jar`
   - Config file: `panda-boots-status.json`
   - Log directory: `config/panda-boots-status/`

#### **Test Backward Compatibility** ✅
1. **Migration test**:
   - Place old `actionbar-logger.json` in config folder
   - Launch mod → Should auto-migrate to `panda-boots-status.json`
   - All settings preserved

### **6. Single Status Display Testing**

#### **Test "Latest Wins" Logic** ✅
```bash
# Rapid replacement test
/title @s actionbar {"text":"Mood Swings IV has made you feel lazy!"}
/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!"}

# Expected behavior:
# 1. First status appears: "😴 LAZY IV 10s"
# 2. Second command IMMEDIATELY replaces with: "😡 AGGRESSIVE V 10s"
# 3. NO multiple statuses shown simultaneously
# 4. Timer resets to 10 seconds for new status
```

#### **Test Timer Functionality** ✅
1. Trigger any mood swing
2. **Verify countdown**: 10s → 9s → 8s → ... → 1s → 0s
3. **Verify expiration**: Status disappears when timer reaches 0
4. **Verify replacement**: New mood swing resets timer to 10s

### **7. Effect Descriptions Testing**

#### **Test Tier-Based Calculations** ✅
```bash
# Test AGGRESSIVE tiers
/title @s actionbar {"text":"Mood Swings I has made you feel aggressive!"}
# Expected: "You are doing 10% more damage" (5% + 5×1)

/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!"}
# Expected: "You are doing 30% more damage" (5% + 5×5)

# Test LAZY tiers
/title @s actionbar {"text":"Mood Swings I has made you feel lazy!"}
# Expected: "You are moving 28% slower" (30% - 2×1)

/title @s actionbar {"text":"Mood Swings V has made you feel lazy!"}
# Expected: "You are moving 20% slower" (30% - 2×5)

# Test PLAYFUL tiers
/title @s actionbar {"text":"Mood Swings III has made you feel playful!"}
# Expected: "You are moving 20% faster" (5% + 5×3)
```

### **8. Background/Border Toggle Testing**

#### **Test Simultaneous Control** ✅
1. **Initial state**: Background and border visible
2. **Click "🎨 Toggle Background & Border"**:
   - Both background AND border disappear together
   - Status text remains visible (floating text)
3. **Click button again**:
   - Both background AND border reappear together
   - **Should NEVER affect only one element**

### **9. Comprehensive Integration Testing**

#### **Test Complete Workflow** ✅
1. **Launch game** → Mod loads successfully
2. **Press 'O'** → Settings screen opens with modern styling
3. **Test all toggles** → Each works independently
4. **Open position screen** → Live preview with dragging works
5. **Trigger mood swing** → Status appears with correct format
6. **Test rapid replacement** → Latest wins logic works
7. **Wait 10 seconds** → Status expires automatically
8. **Test background toggle** → Both elements toggle together

#### **Test Error Handling** ✅
```bash
# Test malformed messages (should be ignored)
/title @s actionbar {"text":"Invalid mood message"}
/title @s actionbar {"text":"Mood Swings has made you feel confused!"}
/title @s actionbar {"text":"Mood Swings X has made you feel aggressive!"}

# Expected: No status display, no crashes
```

## ✅ **Success Criteria**

### **All Tests Must Pass:**
1. ✅ No "Enable Dragging", "Show Progress Bar", or "Show Timer" toggles
2. ✅ "Show Title" toggle controls title visibility only
3. ✅ "Toggle Background & Border" affects both elements simultaneously
4. ✅ Position screen provides live preview with dragging
5. ✅ Status format shows "V 8s" (no dash)
6. ✅ Mood icons toggle affects only mood emoji, not panda emoji
7. ✅ Apple emoji render properly (😡😴😄🐼)
8. ✅ Mod identity shows "Panda Boots Status" everywhere
9. ✅ "Latest wins" logic replaces existing status immediately
10. ✅ Effect descriptions show correct tier-based percentages
11. ✅ Background/border toggle works simultaneously
12. ✅ Backward compatibility migrates old config files

## 🎉 **Final Validation**

**If ALL tests pass, the Panda Boots Status mod is ready for production with:**
- Complete rebranding from Action Bar Logger
- Enhanced UI with readwork integration
- Improved status display format
- Advanced positioning system
- Proper emoji implementation
- Seamless backward compatibility

**Build artifact**: `build/libs/panda-boots-status-1.0.0.jar` ✅
