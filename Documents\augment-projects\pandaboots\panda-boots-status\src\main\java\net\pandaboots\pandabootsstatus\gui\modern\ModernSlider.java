package net.pandaboots.pandabootsstatus.gui.modern;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.text.Text;

import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Modern slider with smooth dragging and value display
 * Adapted from readwork mod's ModernSlider
 */
public class ModernSlider extends ModernWidget {
    
    private final Supplier<Float> valueGetter;
    private final Consumer<Float> valueSetter;
    private final float minValue;
    private final float maxValue;
    private final Function<Float, String> labelFormatter;
    private final String label;
    
    private boolean isDragging = false;
    private static final int SLIDER_HEIGHT = 6;
    private static final int KNOB_SIZE = 14;
    
    public ModernSlider(int x, int y, int width, int height, String label,
                       float minValue, float maxValue,
                       Supplier<Float> getter, Consumer<Float> setter,
                       Function<Float, String> formatter) {
        super(x, y, width, height, Text.literal(label));
        this.label = label;
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.valueGetter = getter;
        this.valueSetter = setter;
        this.labelFormatter = formatter;
    }
    
    @Override
    protected void renderContent(DrawContext context, int mouseX, int mouseY, float delta) {
        float value = valueGetter.get();
        String displayText = label + ": " + labelFormatter.apply(value);

        // Draw label
        var textRenderer = MinecraftClient.getInstance().textRenderer;
        int textX = getX() + 8;
        int textY = getY() + 4;
        context.drawText(textRenderer, displayText, textX, textY, getTextColor(), false);

        // Calculate slider area with proper spacing from text
        int sliderX = getX() + 8;
        int sliderY = getY() + getHeight() - SLIDER_HEIGHT - 6; // Reduced bottom margin
        int sliderWidth = getWidth() - 16;
        
        // Draw slider track
        int trackColor = SECONDARY_COLOR;
        context.fill(sliderX, sliderY, sliderX + sliderWidth, sliderY + SLIDER_HEIGHT, trackColor);
        
        // Calculate knob position
        float normalizedValue = (value - minValue) / (maxValue - minValue);
        int knobX = sliderX + (int) (normalizedValue * (sliderWidth - KNOB_SIZE));
        int knobY = sliderY - (KNOB_SIZE - SLIDER_HEIGHT) / 2;
        
        // Draw filled portion
        int fillColor = ACCENT_COLOR;
        context.fill(sliderX, sliderY, knobX + KNOB_SIZE / 2, sliderY + SLIDER_HEIGHT, fillColor);
        
        // Draw knob
        int knobColor = isHovered || isDragging ? TEXT_COLOR : TEXT_SECONDARY;
        context.fill(knobX, knobY, knobX + KNOB_SIZE, knobY + KNOB_SIZE, knobColor);
        
        // Draw knob border
        context.drawBorder(knobX, knobY, KNOB_SIZE, KNOB_SIZE, getBorderColor());
    }
    
    @Override
    protected void renderBackground(DrawContext context, int mouseX, int mouseY, float delta) {
        int backgroundColor = isHovered || isDragging ? HOVER_COLOR : PRIMARY_COLOR;
        context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), backgroundColor);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0 && isHovered) {
            isDragging = true;
            updateValue(mouseX);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDragging) {
            updateValue(mouseX);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0 && isDragging) {
            isDragging = false;
            return true;
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }
    
    private void updateValue(double mouseX) {
        int sliderX = getX() + 8;
        int sliderWidth = getWidth() - 16;
        
        double normalizedX = (mouseX - sliderX) / sliderWidth;
        normalizedX = Math.max(0.0, Math.min(1.0, normalizedX));
        
        float newValue = minValue + (float) normalizedX * (maxValue - minValue);
        valueSetter.accept(newValue);
    }
    
    @Override
    protected void onPress() {
        // Handled by mouse events
    }
}
