package net.pandaboots.pandabootsstatus.status;

/**
 * Represents an active mood effect with timing and tier information
 */
public class MoodEffect {
    private final String id;
    private final MoodStatus status;
    private final int tier;
    private final long startTime;
    private final int originalDuration; // in seconds
    
    public MoodEffect(String id, MoodStatus status, int tier, int duration) {
        this.id = id;
        this.status = status;
        this.tier = tier;
        this.startTime = System.currentTimeMillis();
        this.originalDuration = duration;
    }
    
    public String getId() {
        return id;
    }
    
    public MoodStatus getStatus() {
        return status;
    }
    
    public int getTier() {
        return tier;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public int getOriginalDuration() {
        return originalDuration;
    }
    
    /**
     * Gets the remaining seconds for this effect
     * @return Remaining seconds (0 if expired)
     */
    public int getRemainingSeconds() {
        long elapsed = System.currentTimeMillis() - startTime;
        long remaining = (originalDuration * 1000L) - elapsed;
        return Math.max(0, (int) (remaining / 1000));
    }
    
    /**
     * Checks if this effect has expired
     * @return true if expired, false otherwise
     */
    public boolean isExpired() {
        return getRemainingSeconds() <= 0;
    }
    
    /**
     * Gets the progress of this effect (0.0 = just started, 1.0 = expired)
     * @return Progress value between 0.0 and 1.0
     */
    public float getProgress() {
        long elapsed = System.currentTimeMillis() - startTime;
        long totalDuration = originalDuration * 1000L;
        return Math.min(1.0f, (float) elapsed / totalDuration);
    }
    
    /**
     * Gets the display text for this effect
     * @param showMoodIcons Whether to show mood icons
     * @return Formatted display text
     */
    public String getDisplayText(boolean showMoodIcons) {
        return status.getDisplayText(tier, getRemainingSeconds(), showMoodIcons);
    }

    /**
     * Gets the display text for this effect (with icons enabled by default)
     * @return Formatted display text
     */
    public String getDisplayText() {
        return status.getDisplayText(tier, getRemainingSeconds());
    }
    
    /**
     * Gets the effect description for this mood effect
     * @return Effect description
     */
    public String getEffectDescription() {
        return status.getEffectDescription(tier);
    }
    
    @Override
    public String toString() {
        return String.format("MoodEffect{id='%s', status=%s, tier=%d, remaining=%ds}", 
                           id, status, tier, getRemainingSeconds());
    }
}
