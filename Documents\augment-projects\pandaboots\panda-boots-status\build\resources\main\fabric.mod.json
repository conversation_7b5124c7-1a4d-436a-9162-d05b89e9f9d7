{"schemaVersion": 1, "id": "panda-boots-status", "version": "1.0.0", "name": "Panda Boots Status", "description": "A client-side mod that displays Panda Boots mood swing status with a modern visual interface", "authors": ["Cerv"], "contact": {"homepage": "https://github.com/cerv/panda-boots-status", "sources": "https://github.com/cerv/panda-boots-status"}, "license": "MIT", "icon": "assets/panda-boots-status/icon.png", "environment": "client", "entrypoints": {"client": ["net.pandaboots.pandabootsstatus.PandaBootsStatus"]}, "mixins": ["panda-boots-status.mixins.json"], "depends": {"fabricloader": ">=0.16.12", "minecraft": "~1.21.1", "java": ">=21", "fabric-api": "*"}, "suggests": {}}