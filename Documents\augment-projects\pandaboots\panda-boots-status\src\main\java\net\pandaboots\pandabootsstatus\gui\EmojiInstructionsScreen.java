package net.pandaboots.pandabootsstatus.gui;

import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.pandaboots.pandabootsstatus.gui.modern.ModernButton;
import net.pandaboots.pandabootsstatus.gui.modern.ModernWidget;
import net.pandaboots.pandabootsstatus.render.FileBasedEmojiLoader;

import java.util.ArrayList;
import java.util.List;

/**
 * Informational screen showing how to configure custom emojis using files
 */
public class EmojiInstructionsScreen extends Screen {
    private final Screen parent;
    private final List<ModernWidget> widgets = new ArrayList<>();
    
    // UI Constants
    private static final int WIDGET_HEIGHT = 30;
    private static final int PADDING = 20;
    private static final int LINE_HEIGHT = 12;
    
    public EmojiInstructionsScreen(Screen parent) {
        super(Text.literal("Custom Emoji Instructions"));
        this.parent = parent;
    }
    
    @Override
    protected void init() {
        super.init();
        widgets.clear();
        
        int contentWidth = Math.min(700, this.width - PADDING * 2);
        int contentX = (this.width - contentWidth) / 2;
        
        // Calculate button position at bottom
        int buttonY = this.height - 60;
        
        // Back button
        ModernButton backButton = new ModernButton(
            contentX, buttonY, 100, WIDGET_HEIGHT,
            "Back", this::close, ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(backButton);
        
        // Reload button
        ModernButton reloadButton = new ModernButton(
            contentX + 110, buttonY, 120, WIDGET_HEIGHT,
            "Reload Emojis", this::reloadEmojis, ModernButton.ButtonStyle.PRIMARY
        );
        widgets.add(reloadButton);
        
        // Open folder button (if possible)
        ModernButton openFolderButton = new ModernButton(
            contentX + 240, buttonY, 140, WIDGET_HEIGHT,
            "Open Config Folder", this::openConfigFolder, ModernButton.ButtonStyle.SUCCESS
        );
        widgets.add(openFolderButton);
    }
    
    private void reloadEmojis() {
        FileBasedEmojiLoader.reload();
        // Could add a confirmation message here
    }
    
    private void openConfigFolder() {
        try {
            String configPath = FileBasedEmojiLoader.getConfigDirectory();
            if (!configPath.equals("Not initialized")) {
                // Try to open the folder in the system file explorer
                java.awt.Desktop desktop = java.awt.Desktop.getDesktop();
                java.io.File configDir = new java.io.File(configPath);
                if (configDir.exists()) {
                    desktop.open(configDir);
                }
            }
        } catch (Exception e) {
            // Silently fail - not all systems support this
        }
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw modern background
        context.fill(0, 0, this.width, this.height, ModernWidget.BACKGROUND_COLOR);
        
        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, ModernWidget.TEXT_COLOR);
        
        // Draw instructions
        int contentWidth = Math.min(700, this.width - PADDING * 2);
        int contentX = (this.width - contentWidth) / 2;
        int currentY = 60;
        
        // Main instructions
        String[] instructions = {
            "Custom Emoji Configuration",
            "",
            "To use custom emojis, place PNG files in your config directory:",
            "",
            "📁 Location: " + FileBasedEmojiLoader.getConfigDirectory(),
            "",
            "📋 Required Files:",
            "  • panda.png - For the panda emoji in the title",
            "  • aggressive.png - For angry/aggressive mood status",
            "  • playful.png - For happy/playful mood status", 
            "  • lazy.png - For sleepy/lazy mood status",
            "",
            "📐 Image Requirements:",
            "  • Format: PNG (recommended)",
            "  • Size: Any size (16x16 recommended for best quality)",
            "  • Processing: Images loaded directly without resizing",
            "  • Rendering: Scaled automatically by Minecraft (like default emojis)",
            "  • Transparency: Supported",
            "",
            "🔄 How to Apply Changes:",
            "  1. Replace the PNG files in the config directory",
            "  2. Click 'Reload Emojis' button below, OR",
            "  3. Restart Minecraft",
            "",
            "💡 Tips:",
            "  • Default placeholder files are created automatically",
            "  • Replace them with your own custom emoji images",
            "  • Keep backups of your custom emojis",
            "  • 16x16 pixel images provide optimal quality (like default emojis)",
            "  • Images are loaded directly without quality-reducing resizing",
            "  • Square images work best for emoji display",
            "",
            "🎨 Current Status: " + getEmojiStatus()
        };
        
        // Draw each instruction line
        for (String line : instructions) {
            if (line.isEmpty()) {
                currentY += LINE_HEIGHT / 2; // Half spacing for empty lines
                continue;
            }
            
            // Use different colors for different types of lines
            int color = ModernWidget.TEXT_COLOR;
            if (line.startsWith("📁") || line.startsWith("📋") || line.startsWith("📐") || 
                line.startsWith("🔄") || line.startsWith("💡") || line.startsWith("🎨")) {
                color = ModernWidget.ACCENT_COLOR; // Headers in accent color
            } else if (line.startsWith("  •") || line.startsWith("  ")) {
                color = 0xFFCCCCCC; // Sub-items in lighter color
            } else if (line.equals("Custom Emoji Configuration")) {
                color = ModernWidget.ACCENT_COLOR; // Main title
            }
            
            context.drawText(this.textRenderer, line, contentX, currentY, color, false);
            currentY += LINE_HEIGHT;
        }
        
        // Render widgets
        for (ModernWidget widget : widgets) {
            widget.render(context, mouseX, mouseY, delta);
        }
    }
    
    private String getEmojiStatus() {
        try {
            String debugInfo = FileBasedEmojiLoader.getDebugInfo();
            // Extract just the loaded count
            if (debugInfo.contains("Loaded emojis: ")) {
                String[] lines = debugInfo.split("\n");
                for (String line : lines) {
                    if (line.contains("Loaded emojis: ")) {
                        String count = line.substring(line.indexOf("Loaded emojis: ") + 15).trim();
                        return count + " custom emojis loaded";
                    }
                }
            }
            return "File-based emoji system active";
        } catch (Exception e) {
            return "Error checking emoji status";
        }
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle widget clicks
        for (ModernWidget widget : widgets) {
            if (widget.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        }
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Handle widget dragging
        for (ModernWidget widget : widgets) {
            if (widget.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }
    
    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        // Handle widget releases
        for (ModernWidget widget : widgets) {
            if (widget.mouseReleased(mouseX, mouseY, button)) {
                return true;
            }
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }
    
    @Override
    public void close() {
        // Return to parent screen
        if (this.client != null) {
            this.client.setScreen(parent);
        }
    }
}
