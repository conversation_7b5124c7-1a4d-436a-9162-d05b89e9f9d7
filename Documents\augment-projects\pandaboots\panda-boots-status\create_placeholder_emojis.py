#!/usr/bin/env python3
"""
Creates placeholder PNG emoji files for the Panda Boots Status mod
"""

from PIL import Image, ImageDraw
import os

def create_emoji_png(filename, base_color, accent_color=None, pattern="circle"):
    """Create a simple emoji PNG file"""
    size = 16
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))  # Transparent background
    draw = ImageDraw.Draw(img)
    
    if pattern == "circle":
        # Draw a filled circle
        draw.ellipse([2, 2, size-2, size-2], fill=base_color, outline=(0, 0, 0, 255))
        
        # Add simple features based on emoji type
        if "aggressive" in filename:
            # Angry eyes (slanted lines)
            draw.line([5, 6, 7, 8], fill=(0, 0, 0, 255), width=1)
            draw.line([9, 8, 11, 6], fill=(0, 0, 0, 255), width=1)
            # Frown
            draw.arc([6, 9, 10, 12], 0, 180, fill=(0, 0, 0, 255))
            
        elif "lazy" in filename:
            # Sleepy eyes (horizontal lines)
            draw.line([5, 7, 7, 7], fill=(0, 0, 0, 255), width=1)
            draw.line([9, 7, 11, 7], fill=(0, 0, 0, 255), width=1)
            # Small mouth
            draw.point([8, 10], fill=(0, 0, 0, 255))
            
        elif "playful" in filename:
            # Happy eyes (dots)
            draw.point([6, 7], fill=(0, 0, 0, 255))
            draw.point([10, 7], fill=(0, 0, 0, 255))
            # Smile
            draw.arc([6, 8, 10, 11], 180, 360, fill=(0, 0, 0, 255))
            
        elif "panda" in filename:
            # Panda face - white base with black features
            draw.ellipse([2, 2, size-2, size-2], fill=(255, 255, 255, 255), outline=(0, 0, 0, 255))
            # Ears
            draw.ellipse([3, 3, 6, 6], fill=(0, 0, 0, 255))
            draw.ellipse([10, 3, 13, 6], fill=(0, 0, 0, 255))
            # Eyes
            draw.point([6, 8], fill=(0, 0, 0, 255))
            draw.point([10, 8], fill=(0, 0, 0, 255))
            # Nose
            draw.point([8, 10], fill=(0, 0, 0, 255))
    
    return img

def main():
    # Define emoji colors and create files
    emojis = {
        "aggressive.png": ((255, 68, 68, 255), None, "circle"),  # Red
        "lazy.png": ((68, 102, 255, 255), None, "circle"),      # Blue
        "playful.png": ((68, 255, 68, 255), None, "circle"),    # Green
        "panda.png": ((255, 255, 255, 255), (0, 0, 0, 255), "circle")  # White with black
    }
    
    # Create output directory
    output_dir = "src/main/resources/assets/panda-boots-status/textures/emoji"
    os.makedirs(output_dir, exist_ok=True)
    
    # Create each emoji
    for filename, (base_color, accent_color, pattern) in emojis.items():
        img = create_emoji_png(filename, base_color, accent_color, pattern)
        filepath = os.path.join(output_dir, filename)
        img.save(filepath)
        print(f"Created {filepath}")
    
    print("All placeholder emoji PNGs created successfully!")

if __name__ == "__main__":
    main()
